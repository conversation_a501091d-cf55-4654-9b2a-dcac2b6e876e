import express from 'express';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 裝備數據（從原始constants移植）
const EQUIPMENT_ITEMS = [
  {
    id: 'sword_of_avarice',
    name: '貪婪之劍',
    icon: '🗡️',
    cost: 100,
    slot: 'weapon',
    attack: 8,
    bonus: {
      type: 'xp_gain',
      value: 0.1,
      description: '+10% 經驗值獲取'
    }
  },
  {
    id: 'merchants_garb',
    name: '商賈之袍',
    icon: '🧥',
    cost: 150,
    slot: 'armor',
    defense: 5,
    bonus: {
      type: 'tax_rate',
      value: 0.15,
      description: '+15% 稅收'
    }
  }
];

// 獲取所有裝備
router.get('/', (req, res) => {
  try {
    res.json({
      equipment: EQUIPMENT_ITEMS
    });
  } catch (error) {
    console.error('獲取裝備列表錯誤:', error);
    res.status(500).json({
      message: '獲取裝備列表失敗'
    });
  }
});

// 獲取單個裝備詳情
router.get('/:id', (req, res) => {
  try {
    const equipment = EQUIPMENT_ITEMS.find(item => item.id === req.params.id);
    
    if (!equipment) {
      return res.status(404).json({
        message: '裝備不存在'
      });
    }

    res.json(equipment);
  } catch (error) {
    console.error('獲取裝備詳情錯誤:', error);
    res.status(500).json({
      message: '獲取裝備詳情失敗'
    });
  }
});

export default router;

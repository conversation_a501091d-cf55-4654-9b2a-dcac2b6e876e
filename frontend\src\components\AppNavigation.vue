<template>
  <nav class="bg-stone-900/80 border-b-2 border-stone-800 backdrop-blur-sm">
    <div class="container mx-auto px-4">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center space-x-4">
          <h1 class="text-2xl font-bold text-yellow-500 font-pixel">
            財富冒險
          </h1>
        </div>

        <!-- 桌面導航 -->
        <div class="hidden md:flex items-center space-x-6">
          <router-link
            v-for="item in navigationItems"
            :key="item.name"
            :to="item.path"
            class="nav-link"
            :class="{ 'active': $route.name === item.name }"
          >
            <span class="text-xl">{{ item.icon }}</span>
            <span class="font-bold">{{ item.label }}</span>
            <span 
              v-if="item.badge && item.badge > 0"
              class="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center"
            >
              {{ item.badge }}
            </span>
          </router-link>
        </div>

        <!-- 用戶菜單 -->
        <div class="flex items-center space-x-4">
          <!-- 水晶數量 -->
          <div class="flex items-center space-x-2 bg-stone-800 px-3 py-1 rounded-lg">
            <span class="text-blue-400">💎</span>
            <span class="font-bold text-blue-400">{{ characterStore.crystals }}</span>
          </div>

          <!-- 等級 -->
          <div class="flex items-center space-x-2 bg-stone-800 px-3 py-1 rounded-lg">
            <span class="text-yellow-400">⭐</span>
            <span class="font-bold text-yellow-400">Lv.{{ characterStore.level }}</span>
          </div>

          <!-- 用戶下拉菜單 -->
          <div class="relative" ref="userMenuRef">
            <button
              @click="toggleUserMenu"
              class="flex items-center space-x-2 bg-stone-800 hover:bg-stone-700 px-3 py-2 rounded-lg transition-colors"
            >
              <span class="text-2xl">{{ authStore.user?.avatar || '🧙‍♂️' }}</span>
              <span class="font-bold">{{ authStore.user?.username }}</span>
              <ChevronDownIcon class="w-4 h-4" />
            </button>

            <!-- 下拉菜單 -->
            <div
              v-show="showUserMenu"
              class="absolute right-0 mt-2 w-48 bg-stone-800 border border-stone-700 rounded-lg shadow-lg z-50"
            >
              <router-link
                to="/profile"
                class="block px-4 py-2 hover:bg-stone-700 transition-colors"
                @click="showUserMenu = false"
              >
                <UserIcon class="w-4 h-4 inline mr-2" />
                個人資料
              </router-link>
              <hr class="border-stone-700" />
              <button
                @click="handleLogout"
                class="w-full text-left px-4 py-2 hover:bg-stone-700 transition-colors text-red-400"
              >
                <ArrowRightOnRectangleIcon class="w-4 h-4 inline mr-2" />
                登出
              </button>
            </div>
          </div>
        </div>

        <!-- 移動端菜單按鈕 -->
        <button
          @click="toggleMobileMenu"
          class="md:hidden p-2 rounded-lg hover:bg-stone-700 transition-colors"
        >
          <Bars3Icon class="w-6 h-6" />
        </button>
      </div>

      <!-- 移動端導航 -->
      <div v-show="showMobileMenu" class="md:hidden py-4 border-t border-stone-700">
        <div class="flex flex-col space-y-2">
          <router-link
            v-for="item in navigationItems"
            :key="item.name"
            :to="item.path"
            class="nav-link-mobile"
            :class="{ 'active': $route.name === item.name }"
            @click="showMobileMenu = false"
          >
            <span class="text-xl">{{ item.icon }}</span>
            <span class="font-bold">{{ item.label }}</span>
            <span 
              v-if="item.badge && item.badge > 0"
              class="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center ml-auto"
            >
              {{ item.badge }}
            </span>
          </router-link>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useCharacterStore } from '@/stores/character'
import { 
  ChevronDownIcon, 
  UserIcon, 
  ArrowRightOnRectangleIcon,
  Bars3Icon 
} from '@heroicons/vue/24/outline'

const router = useRouter()
const authStore = useAuthStore()
const characterStore = useCharacterStore()

const showUserMenu = ref(false)
const showMobileMenu = ref(false)
const userMenuRef = ref<HTMLElement>()

const navigationItems = computed(() => [
  {
    name: 'Dashboard',
    path: '/dashboard',
    label: '儀表板',
    icon: '📊'
  },
  {
    name: 'Character',
    path: '/character',
    label: '角色',
    icon: '🧑‍'
  },
  {
    name: 'Shop',
    path: '/shop',
    label: '商店',
    icon: '🏪'
  },
  {
    name: 'Talents',
    path: '/talents',
    label: '天賦',
    icon: '✨',
    badge: characterStore.talentPoints
  },
  {
    name: 'Town',
    path: '/town',
    label: '城鎮',
    icon: '🏰'
  },
  {
    name: 'Exploration',
    path: '/exploration',
    label: '探索',
    icon: '🗺️'
  }
])

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('登出失敗:', error)
  }
  showUserMenu.value = false
}

// 點擊外部關閉菜單
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.nav-link {
  @apply flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors text-stone-400 hover:text-white hover:bg-stone-700;
}

.nav-link.active {
  @apply text-yellow-300 bg-yellow-600/20;
}

.nav-link-mobile {
  @apply flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors text-stone-400 hover:text-white hover:bg-stone-700;
}

.nav-link-mobile.active {
  @apply text-yellow-300 bg-yellow-600/20;
}
</style>

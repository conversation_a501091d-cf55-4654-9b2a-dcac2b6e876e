import React, { useState, useCallback } from 'react';
import { getFinancialAdvice } from '../services/geminiService';

interface FinancialSummary {
    income: number;
    expenses: number;
    balance: number;
    expenseByCategory: Record<string, number>;
}

interface WiseSageModalProps {
    isOpen: boolean;
    onClose: () => void;
    summary: FinancialSummary;
}

const WiseSageModal: React.FC<WiseSageModalProps> = ({ isOpen, onClose, summary }) => {
    const [advice, setAdvice] = useState<string>('');
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    const handleGetAdvice = useCallback(async () => {
        setIsLoading(true);
        setError(null);
        setAdvice('');
        try {
            const result = await getFinancialAdvice(summary);
            setAdvice(result);
        } catch (err) {
            setError('智者正在冥想，無法聯繫。請稍後再試。');
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    }, [summary]);

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4" onClick={onClose}>
            <div
                className="bg-stone-800 p-6 rounded-lg border-2 border-yellow-700 shadow-2xl w-full max-w-2xl transform transition-all duration-300"
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-2xl font-bold text-yellow-400 font-pixel">智者</h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-white">&times;</button>
                </div>
                <div className="space-y-4">
                    {!advice && !isLoading && (
                         <div className="text-center">
                            <p className="text-gray-300 mb-6">將您的帳本呈現給智者，並為您的財務之旅尋求指導。</p>
                            <button
                                onClick={handleGetAdvice}
                                className="bg-purple-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-purple-500 transition-colors duration-300 shadow-md border-b-4 border-purple-800 hover:border-purple-700 active:border-b-0 active:translate-y-1"
                            >
                                尋求建議
                            </button>
                        </div>
                    )}
                   
                    {isLoading && (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto"></div>
                            <p className="mt-4 text-gray-400">智者正在查閱古老的卷軸...</p>
                        </div>
                    )}
                    {error && <p className="text-red-400 bg-red-900/50 p-3 rounded-md">{error}</p>}
                    {advice && (
                        <div className="bg-stone-900/50 p-4 rounded-md border border-gray-600 max-h-80 overflow-y-auto">
                            <p className="text-yellow-200 italic whitespace-pre-wrap">{advice}</p>
                             <button
                                onClick={handleGetAdvice}
                                className="mt-4 bg-gray-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-gray-500 transition-colors duration-300 text-sm"
                            >
                                再問一次
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default WiseSageModal;
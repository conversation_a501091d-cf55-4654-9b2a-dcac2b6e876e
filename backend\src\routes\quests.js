import express from 'express';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 任務數據（從原始constants移植）
const INITIAL_QUESTS = [
  {
    id: 'earn_first_gold',
    title: '第一次收入',
    description: '記錄你的第一筆收入。',
    reward: 10,
    condition: { type: 'earn', amount: 1 }
  },
  {
    id: 'first_expense',
    title: '第一次花費',
    description: '記錄你的第一筆支出。',
    reward: 5,
    condition: { type: 'spend', amount: 1 }
  },
  {
    id: 'supply_run',
    title: '補給採買',
    description: '進行 3 次「補給品」類別的消費。',
    reward: 20,
    condition: { type: 'spend', amount: 3, category: '補給品' }
  },
  {
    id: 'balance_1000',
    title: '小有積蓄',
    description: '讓你的金幣餘額達到 1000。',
    reward: 50,
    condition: { type: 'balance', amount: 1000 }
  }
];

// 獲取所有任務
router.get('/', (req, res) => {
  try {
    res.json({
      quests: INITIAL_QUESTS
    });
  } catch (error) {
    console.error('獲取任務列表錯誤:', error);
    res.status(500).json({
      message: '獲取任務列表失敗'
    });
  }
});

// 獲取單個任務詳情
router.get('/:id', (req, res) => {
  try {
    const quest = INITIAL_QUESTS.find(q => q.id === req.params.id);
    
    if (!quest) {
      return res.status(404).json({
        message: '任務不存在'
      });
    }

    res.json(quest);
  } catch (error) {
    console.error('獲取任務詳情錯誤:', error);
    res.status(500).json({
      message: '獲取任務詳情失敗'
    });
  }
});

export default router;

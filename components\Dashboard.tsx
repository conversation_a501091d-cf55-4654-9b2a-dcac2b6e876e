import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON>A<PERSON><PERSON>, <PERSON>lt<PERSON>, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell, Legend } from 'recharts';
import { Transaction, TransactionType, Category } from '../types';

// --- StatBox Component ---
interface StatBoxProps {
    label: string;
    value: string | number;
    icon: string;
    className?: string;
}

const StatBox: React.FC<StatBoxProps> = ({ label, value, icon, className }) => (
    <div className="bg-stone-900/50 p-4 rounded-lg flex items-center space-x-4 border border-gray-700">
        <div className="text-3xl">{icon}</div>
        <div>
            <div className="text-sm text-gray-400">{label}</div>
            <div className={`text-xl font-bold font-pixel ${className || 'text-yellow-300'}`}>{value}</div>
        </div>
    </div>
);

// --- Financial Analysis Chart Component ---
const FinancialCharts: React.FC<{ transactions: Transaction[], expenseByCategory: Record<Category, number>, incomeByCategory: Record<Category, number> }> = ({ transactions, expenseByCategory, incomeByCategory }) => {
    const [chartType, setChartType] = useState<'expense' | 'income'>('expense');
    const [timeframe, setTimeframe] = useState<'daily' | 'monthly'>('daily');

    const chartData = useMemo(() => {
        const typeFilter = chartType === 'expense' ? TransactionType.EXPENSE : TransactionType.INCOME;
        const filteredTransactions = transactions.filter(t => t.type === typeFilter && t.date);

        if (timeframe === 'daily') {
            const last30Days = Array.from({ length: 30 }, (_, i) => {
                const d = new Date();
                d.setDate(d.getDate() - i);
                return d.toISOString().split('T')[0];
            }).reverse();

            const dailyData: { [key: string]: number } = last30Days.reduce((acc, date) => ({ ...acc, [date]: 0 }), {});

            filteredTransactions.forEach(t => {
                const date = t.date.split('T')[0];
                if (dailyData[date] !== undefined) {
                    dailyData[date] += t.amount;
                }
            });

            return Object.entries(dailyData).map(([date, amount]) => ({
                name: new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
                amount,
            }));
        } else { // monthly
            const currentYear = new Date().getFullYear();
            const monthlyData = Array.from({ length: 12 }, (_, i) => ({
                name: new Date(currentYear, i).toLocaleString('default', { month: 'short' }),
                amount: 0
            }));
            
            filteredTransactions.forEach(t => {
                const transactionDate = new Date(t.date);
                if (transactionDate.getFullYear() === currentYear) {
                    const month = transactionDate.getMonth();
                    monthlyData[month].amount += t.amount;
                }
            });
            return monthlyData;
        }
    }, [transactions, chartType, timeframe]);

    const pieChartData = useMemo(() => {
        const sourceData = chartType === 'expense' ? expenseByCategory : incomeByCategory;
        return Object.entries(sourceData)
            .map(([name, value]) => ({ name, value }))
            .sort((a, b) => b.value - a.value);
    }, [expenseByCategory, incomeByCategory, chartType]);

    const COLORS = ['#a13c3c', '#4a7a4c', '#8c6f4c', '#5a7d9a', '#7a5c8e', '#c87f4a'];

    const ChartButton: React.FC<{ active: boolean, onClick: () => void, children: React.ReactNode }> = ({ active, onClick, children }) => (
        <button
            onClick={onClick}
            className={`px-3 py-1 text-xs rounded-md transition-colors ${
                active ? 'bg-yellow-600 text-stone-900 font-bold' : 'bg-stone-700 hover:bg-stone-600'
            }`}
        >
            {children}
        </button>
    );

    return (
         <div className="bg-stone-800/80 p-6 rounded-lg border-2 border-yellow-800 shadow-lg">
            <h3 className="text-2xl font-bold mb-4 text-yellow-400 font-pixel">財務分析中心</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <div className="flex justify-between items-center mb-4">
                         <div className="space-x-2">
                            <ChartButton active={chartType === 'expense'} onClick={() => setChartType('expense')}>支出分析</ChartButton>
                            <ChartButton active={chartType === 'income'} onClick={() => setChartType('income')}>收入分析</ChartButton>
                        </div>
                        <div className="space-x-2">
                             <ChartButton active={timeframe === 'daily'} onClick={() => setTimeframe('daily')}>每日</ChartButton>
                             <ChartButton active={timeframe === 'monthly'} onClick={() => setTimeframe('monthly')}>每月</ChartButton>
                        </div>
                    </div>
                    <ResponsiveContainer width="100%" height={300}>
                        <BarChart data={chartData} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
                            <XAxis dataKey="name" stroke="#a1a1aa" fontSize={12} tickLine={false} axisLine={false} />
                            <YAxis stroke="#a1a1aa" fontSize={12} tickLine={false} axisLine={false} />
                            <Tooltip
                                contentStyle={{
                                    backgroundColor: '#292524',
                                    border: '1px solid #71717a',
                                    color: '#f5f5f4',
                                }}
                                cursor={{ fill: 'rgba(234, 179, 8, 0.1)' }}
                            />
                            <Bar dataKey="amount" fill={chartType === 'expense' ? '#ef4444' : '#22c55e'} radius={[4, 4, 0, 0]} />
                        </BarChart>
                    </ResponsiveContainer>
                </div>
                <div>
                     <h4 className="text-lg font-bold text-center mb-4 text-yellow-300">
                        {chartType === 'expense' ? '支出類別分佈' : '收入類別分佈'}
                     </h4>
                     <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                             <Pie
                                data={pieChartData}
                                cx="50%"
                                cy="50%"
                                labelLine={false}
                                outerRadius={100}
                                fill="#8884d8"
                                dataKey="value"
                                nameKey="name"
                                // FIX: Explicitly type the label props as 'any' to resolve TypeScript error.
                                // Recharts type inference for render props can be inconsistent.
                                label={({ name, percent }: any) => `${name} ${(percent * 100).toFixed(0)}%`}
                            >
                                {pieChartData.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                ))}
                            </Pie>
                            <Tooltip
                                 contentStyle={{
                                    backgroundColor: '#292524',
                                    border: '1px solid #71717a',
                                    color: '#f5f5f4',
                                }}
                            />
                        </PieChart>
                    </ResponsiveContainer>
                </div>
            </div>
        </div>
    );
}


// --- Main Dashboard Component ---
interface DashboardProps {
    level: number;
    xp: number;
    xpForNextLevel: number;
    crystals: number;
    totalIncome: number;
    totalExpenses: number;
    balance: number;
    onOpenSage: () => void;
    transactions: Transaction[];
    expenseByCategory: Record<Category, number>;
    incomeByCategory: Record<Category, number>;
}

const Dashboard: React.FC<DashboardProps> = ({
    level, xp, xpForNextLevel, crystals, totalIncome,
    totalExpenses, balance, onOpenSage, transactions, expenseByCategory, incomeByCategory
}) => {
    const xpPercentage = xpForNextLevel > 0 ? (xp / xpForNextLevel) * 100 : 0;

    return (
        <div className="space-y-8">
            <div className="bg-stone-800/80 p-6 rounded-lg border-2 border-yellow-800 shadow-lg">
                <div className="grid grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                    <StatBox label="等級" value={level} icon="⭐" />
                    <StatBox label="水晶" value={crystals} icon="💎" />
                    <StatBox label="總收入" value={`${totalIncome} G`} icon="💰" className="text-green-400" />
                    <StatBox label="總支出" value={`${totalExpenses} G`} icon="💸" className="text-red-400" />
                    <StatBox label="本期盈餘" value={`${balance} G`} icon="⚖️" className={balance >= 0 ? 'text-blue-400' : 'text-orange-500'} />
                </div>

                <div className="mb-6">
                    <div className="flex justify-between items-center mb-1 text-sm">
                        <span className="font-bold text-yellow-400">經驗值</span>
                        <span className="text-gray-400 font-pixel">{xp} / {xpForNextLevel}</span>
                    </div>
                    <div className="w-full bg-stone-900 rounded-full h-4 border-2 border-gray-600">
                        <div
                            className="bg-purple-600 h-full rounded-full transition-all duration-500"
                            style={{ width: `${xpPercentage}%` }}
                        ></div>
                    </div>
                </div>

                 <div className="flex justify-between items-center bg-stone-900/60 p-4 rounded-lg border border-gray-700">
                    <div>
                        <div className="text-lg text-gray-300">目前金幣</div>
                        <div className="text-4xl font-bold font-pixel text-yellow-500">{balance} G</div>
                    </div>
                    <button 
                        onClick={onOpenSage}
                        className="bg-purple-600 text-white font-bold py-3 px-5 rounded-lg hover:bg-purple-500 transition-colors duration-300 shadow-md border-b-4 border-purple-800 hover:border-purple-700 active:border-b-0 active:translate-y-1"
                    >
                        <span className="text-2xl mr-2">🧙‍♂️</span>
                        請教智者
                    </button>
                </div>
            </div>
            
            <FinancialCharts 
                transactions={transactions} 
                expenseByCategory={expenseByCategory} 
                incomeByCategory={incomeByCategory} 
            />
        </div>
    );
};

export default Dashboard;
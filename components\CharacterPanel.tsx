
import React, { useState } from 'react';
import { EquipmentSlot, EquipmentItem } from '../types';
import { EQUIPMENT_ITEMS, MONSTER_PARTS } from '../constants';

interface CharacterPanelProps {
    ownedEquipment: string[];
    equippedItems: Record<EquipmentSlot, string | null>;
    onEquipItem: (item: EquipmentItem) => void;
    onUnequipItem: (slot: EquipmentSlot) => void;
    inventory: Record<string, number>;
    playerStats: {
        attack: number;
        defense: number;
    };
}

const CharacterPanel: React.FC<CharacterPanelProps> = ({ ownedEquipment, equippedItems, onEquipItem, onUnequipItem, inventory, playerStats }) => {
    const [activeTab, setActiveTab] = useState<'equipment' | 'materials'>('equipment');

    const equipmentInventory = EQUIPMENT_ITEMS.filter(item => 
        ownedEquipment.includes(item.id) && !Object.values(equippedItems).includes(item.id)
    );

    const renderSlot = (slot: EquipmentSlot, name: string) => {
        const itemId = equippedItems[slot];
        const item = itemId ? EQUIPMENT_ITEMS.find(i => i.id === itemId) : null;
        
        return (
            <div className="bg-stone-900/50 p-3 rounded-lg border border-gray-700">
                <h4 className="text-sm font-bold text-yellow-400 mb-2">{name}</h4>
                {item ? (
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <span className="text-3xl">{item.icon}</span>
                            <div>
                                <p className="font-semibold">{item.name}</p>
                                <p className="text-xs text-green-400">
                                    {item.attack ? `攻擊: +${item.attack} ` : ''}
                                    {item.defense ? `防禦: +${item.defense}` : ''}
                                </p>
                            </div>
                        </div>
                        <button onClick={() => onUnequipItem(slot)} className="text-xs bg-red-800/70 hover:bg-red-700 rounded px-2 py-1">卸下</button>
                    </div>
                ) : (
                    <p className="text-gray-500 text-sm">-- 空 --</p>
                )}
            </div>
        );
    };

    const TabButton: React.FC<{ isActive: boolean, onClick: () => void, children: React.ReactNode }> = ({ isActive, onClick, children }) => (
         <button
            onClick={onClick}
            className={`px-4 py-2 text-sm font-bold rounded-t-lg transition-colors ${
                isActive ? 'bg-stone-800/80 text-yellow-300 border-b-2 border-yellow-400' : 'bg-stone-900/50 text-gray-400 hover:bg-stone-700/50'
            }`}
        >
            {children}
        </button>
    );

    return (
        <div className="bg-stone-800/80 p-6 rounded-lg border-2 border-yellow-800 shadow-lg">
            <div className="flex border-b-2 border-stone-700 mb-4">
                <TabButton isActive={activeTab === 'equipment'} onClick={() => setActiveTab('equipment')}>裝備</TabButton>
                <TabButton isActive={activeTab === 'materials'} onClick={() => setActiveTab('materials')}>材料</TabButton>
            </div>
            
            {activeTab === 'equipment' && (
                <div>
                    <h3 className="text-xl font-bold mb-4 text-yellow-400 font-pixel">角色屬性</h3>
                    <div className="flex space-x-4 mb-6 bg-stone-900/50 p-3 rounded-md">
                        <div className="text-center">
                            <div className="text-sm text-gray-400">攻擊</div>
                            <div className="text-2xl font-pixel text-red-400">{playerStats.attack}</div>
                        </div>
                         <div className="text-center">
                            <div className="text-sm text-gray-400">防禦</div>
                            <div className="text-2xl font-pixel text-blue-400">{playerStats.defense}</div>
                        </div>
                    </div>

                    <h3 className="text-xl font-bold mb-2 text-yellow-400 font-pixel">已裝備</h3>
                    <div className="space-y-4 mb-6">
                        {renderSlot(EquipmentSlot.WEAPON, '武器')}
                        {renderSlot(EquipmentSlot.ARMOR, '護甲')}
                    </div>

                    <h3 className="text-xl font-bold mb-2 text-yellow-400 font-pixel">物品欄</h3>
                    <ul className="space-y-2 max-h-48 overflow-y-auto pr-2">
                        {equipmentInventory.length > 0 ? equipmentInventory.map(item => (
                            <li key={item.id} className="flex items-center justify-between bg-stone-900/50 p-2 rounded-md">
                                 <div className="flex items-center space-x-3">
                                    <span className="text-2xl">{item.icon}</span>
                                    <div>
                                        <p className="font-semibold text-sm">{item.name}</p>
                                        <p className="text-xs text-green-400">
                                            {item.attack ? `攻擊: +${item.attack} ` : ''}
                                            {item.defense ? `防禦: +${item.defense}` : ''}
                                        </p>
                                    </div>
                                </div>
                                <button onClick={() => onEquipItem(item)} className="text-xs bg-green-800/70 hover:bg-green-700 rounded px-2 py-1">裝備</button>
                            </li>
                        )) : (
                            <p className="text-gray-500 text-sm text-center py-4">物品欄是空的。</p>
                        )}
                    </ul>
                </div>
            )}
            
            {activeTab === 'materials' && (
                 <div>
                    <h3 className="text-xl font-bold mb-4 text-yellow-400 font-pixel">製作材料</h3>
                     <ul className="space-y-2 max-h-[350px] overflow-y-auto pr-2">
                        {Object.keys(inventory).length > 0 ? Object.entries(inventory).map(([partId, quantity]) => {
                            const part = MONSTER_PARTS.find(p => p.id === partId);
                            if (!part) return null;
                            return (
                                 <li key={partId} className="flex items-center justify-between bg-stone-900/50 p-2 rounded-md">
                                     <div className="flex items-center space-x-3">
                                        <span className="text-2xl">{part.icon}</span>
                                        <p className="font-semibold text-sm">{part.name}</p>
                                    </div>
                                    <div className="font-pixel text-lg text-blue-300">
                                        x{quantity}
                                    </div>
                                </li>
                            );
                        }) : (
                            <p className="text-gray-500 text-sm text-center py-4">你沒有任何製作材料。</p>
                        )}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default CharacterPanel;

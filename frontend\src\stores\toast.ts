import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { ToastMessage, ToastType } from '@/types'
import { v4 as uuidv4 } from 'uuid'

export const useToastStore = defineStore('toast', () => {
  // 狀態
  const toasts = ref<ToastMessage[]>([])

  // 添加通知
  const addToast = (message: string, type: ToastType = 'info', duration: number = 4000) => {
    const id = uuidv4()
    const toast: ToastMessage = {
      id,
      message,
      type,
      duration
    }

    toasts.value.unshift(toast)

    // 自動移除通知
    setTimeout(() => {
      removeToast(id)
    }, duration)

    return id
  }

  // 移除通知
  const removeToast = (id: string) => {
    const index = toasts.value.findIndex(toast => toast.id === id)
    if (index > -1) {
      toasts.value.splice(index, 1)
    }
  }

  // 清除所有通知
  const clearToasts = () => {
    toasts.value = []
  }

  // 便捷方法
  const success = (message: string, duration?: number) => 
    addToast(message, 'success', duration)

  const error = (message: string, duration?: number) => 
    addToast(message, 'error', duration)

  const warning = (message: string, duration?: number) => 
    addToast(message, 'warning', duration)

  const info = (message: string, duration?: number) => 
    addToast(message, 'info', duration)

  const reward = (message: string, duration?: number) => 
    addToast(message, 'reward', duration)

  return {
    // 狀態
    toasts,
    
    // 方法
    addToast,
    removeToast,
    clearToasts,
    success,
    error,
    warning,
    info,
    reward
  }
})

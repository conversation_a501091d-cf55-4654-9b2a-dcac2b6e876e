<template>
  <div class="p-4 md:p-8">
    <h1 class="text-3xl font-bold text-yellow-400 font-pixel mb-8">個人資料</h1>
    
    <div class="max-w-2xl mx-auto">
      <div class="card">
        <h2 class="text-xl font-bold text-stone-300 mb-6">用戶信息</h2>
        
        <div v-if="authStore.user" class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-stone-400 mb-2">用戶名</label>
            <div class="text-lg text-stone-200">{{ authStore.user.username }}</div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-stone-400 mb-2">電子郵件</label>
            <div class="text-lg text-stone-200">{{ authStore.user.email }}</div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-stone-400 mb-2">註冊時間</label>
            <div class="text-lg text-stone-200">{{ formatDate(authStore.user.createdAt) }}</div>
          </div>
        </div>
        
        <div class="mt-8 pt-6 border-t border-stone-700">
          <button
            @click="handleLogout"
            class="btn-secondary w-full"
          >
            登出
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToastStore } from '@/stores/toast'

const router = useRouter()
const authStore = useAuthStore()
const toastStore = useToastStore()

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    toastStore.success('已成功登出')
    router.push('/login')
  } catch (error) {
    toastStore.error('登出失敗')
  }
}
</script>

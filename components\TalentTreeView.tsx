// Fix: Create full content for TalentTreeView.tsx to implement the component.
import React, { useMemo } from 'react';
import { Talent } from '../types';
import { TALENT_TREE } from '../constants';

interface TalentTreeViewProps {
    level: number;
    talentPoints: number;
    unlockedTalents: string[];
    onUnlockTalent: (talent: Talent) => void;
}

const TalentNode: React.FC<{
    talent: Talent;
    isUnlocked: boolean;
    canUnlock: boolean;
    onClick: () => void;
}> = ({ talent, isUnlocked, canUnlock, onClick }) => {
    const stateClasses = isUnlocked
        ? "bg-yellow-600/80 border-yellow-400 shadow-[0_0_15px_rgba(250,204,21,0.5)]"
        : canUnlock
        ? "bg-stone-700 border-gray-500 hover:bg-green-700 hover:border-green-500 cursor-pointer"
        : "bg-stone-900 border-gray-800 opacity-60 cursor-not-allowed";

    return (
        <div 
            className={`w-full bg-stone-800/50 p-3 rounded-lg border-2 flex items-center space-x-4 transition-all duration-300 ${stateClasses}`}
            onClick={onClick}
            title={talent.description}
        >
            <div className="text-4xl flex-shrink-0">{talent.icon}</div>
            <div className="flex-grow">
                <h4 className="font-bold text-white">{talent.name}</h4>
                <p className="text-xs text-gray-300">{talent.description}</p>
                <div className="text-xs text-gray-400 mt-1">
                    <span>需要等級: {talent.requiredLevel}</span>
                    <span className="mx-2">|</span>
                    <span>費用: {talent.cost} 點</span>
                </div>
            </div>
            {canUnlock && (
                <div className="w-20 text-center">
                     <button className="bg-green-600 text-white font-bold py-1 px-3 rounded text-sm hover:bg-green-500">
                        解鎖
                    </button>
                </div>
            )}
             {isUnlocked && (
                <div className="w-20 text-center text-yellow-300 font-bold text-sm">
                    已擁有
                </div>
            )}
        </div>
    );
};

const TalentTreeView: React.FC<TalentTreeViewProps> = ({ level, talentPoints, unlockedTalents, onUnlockTalent }) => {

    const talentBranches = useMemo(() => {
        return Object.entries(TALENT_TREE).map(([category, talents]) => {
            const roots = talents.filter(t => !t.prerequisite);
            const branches = roots.map(root => {
                const branch = [root];
                let current = root;
                while (current) {
                    const next = talents.find(t => t.prerequisite === current.id);
                    if (next) {
                        branch.push(next);
                        current = next;
                    } else {
                        current = null;
                    }
                }
                return branch;
            });
            return { category, branches };
        });
    }, []);


    return (
        <div className="bg-stone-800/80 p-6 rounded-lg border-2 border-yellow-800 shadow-lg">
            <div className="flex justify-between items-center mb-6">
                 <h2 className="text-2xl font-bold text-yellow-400 font-pixel">天賦樹</h2>
                 <div className="text-lg font-pixel text-purple-400">天賦點數: {talentPoints} ✨</div>
            </div>
           
            <div className="space-y-10 max-h-[500px] overflow-y-auto pr-4">
                {talentBranches.map(({ category, branches }) => (
                    <div key={category}>
                        <h3 className="text-xl font-bold text-yellow-300 mb-4 capitalize font-pixel tracking-wider">{category}</h3>
                        <div className="flex space-x-6">
                            {branches.map((branch, index) => (
                                <div key={index} className="flex-1 flex flex-col items-center space-y-2">
                                    {branch.map((talent, talentIndex) => {
                                        const isUnlocked = unlockedTalents.includes(talent.id);
                                        const prereqUnlocked = !talent.prerequisite || unlockedTalents.includes(talent.prerequisite);
                                        const canUnlock = !isUnlocked && level >= talent.requiredLevel && talentPoints >= talent.cost && prereqUnlocked;
                                        
                                        const isPrevUnlocked = talentIndex > 0 && unlockedTalents.includes(branch[talentIndex-1].id);

                                        return (
                                            <React.Fragment key={talent.id}>
                                                {talentIndex > 0 && (
                                                     <div className={`w-1 h-8 rounded-full ${isUnlocked && isPrevUnlocked ? 'bg-yellow-500' : 'bg-gray-600'}`}></div>
                                                )}
                                                <TalentNode
                                                    talent={talent}
                                                    isUnlocked={isUnlocked}
                                                    canUnlock={canUnlock}
                                                    onClick={() => canUnlock && onUnlockTalent(talent)}
                                                />
                                            </React.Fragment>
                                        );
                                    })}
                                </div>
                            ))}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default TalentTreeView;
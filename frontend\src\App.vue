<template>
  <div id="app" class="min-h-screen bg-stone-900 text-stone-100">
    <!-- 背景圖片 -->
    <div 
      class="fixed inset-0 bg-cover bg-center bg-no-repeat opacity-20"
      style="background-image: url('/fantasy-bg.jpg')"
    ></div>
    
    <!-- 主要內容 -->
    <div class="relative z-10">
      <!-- 導航欄（僅在已登入時顯示） -->
      <AppNavigation v-if="authStore.isAuthenticated" />
      
      <!-- 路由視圖 -->
      <main class="container mx-auto">
        <router-view />
      </main>
      
      <!-- 全局通知 -->
      <ToastContainer />
      
      <!-- 戰鬥模態框 -->
      <CombatModal v-if="combatStore.isActive" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useCharacterStore } from '@/stores/character'
import { useCombatStore } from '@/stores/combat'
import AppNavigation from '@/components/AppNavigation.vue'
import ToastContainer from '@/components/ToastContainer.vue'
import CombatModal from '@/components/CombatModal.vue'

const authStore = useAuthStore()
const characterStore = useCharacterStore()
const combatStore = useCombatStore()

onMounted(async () => {
  // 嘗試恢復認證狀態
  const token = localStorage.getItem('auth_token')
  if (token && !authStore.isAuthenticated) {
    try {
      await authStore.verifyToken()
      // 如果認證成功，獲取角色信息
      if (authStore.isAuthenticated) {
        await characterStore.fetchCharacter()
      }
    } catch (error) {
      console.error('自動登入失敗:', error)
    }
  }
})
</script>

<style>
/* 全局樣式已在 style.css 中定義 */
</style>

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Transaction, TransactionType, Category, PaginationInfo } from '@/types'
import { transactionApi } from '@/services/api'

export const useTransactionStore = defineStore('transaction', () => {
  // 狀態
  const transactions = ref<Transaction[]>([])
  const pagination = ref<PaginationInfo | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 計算屬性
  const totalIncome = computed(() => {
    return transactions.value
      .filter(t => t.type === TransactionType.INCOME)
      .reduce((sum, t) => sum + t.amount, 0)
  })

  const totalExpenses = computed(() => {
    return transactions.value
      .filter(t => t.type === TransactionType.EXPENSE)
      .reduce((sum, t) => sum + t.amount, 0)
  })

  const balance = computed(() => totalIncome.value - totalExpenses.value)

  const expenseByCategory = computed(() => {
    const categories: Record<Category, number> = {} as Record<Category, number>
    transactions.value
      .filter(t => t.type === TransactionType.EXPENSE)
      .forEach(t => {
        categories[t.category] = (categories[t.category] || 0) + t.amount
      })
    return categories
  })

  const incomeByCategory = computed(() => {
    const categories: Record<Category, number> = {} as Record<Category, number>
    transactions.value
      .filter(t => t.type === TransactionType.INCOME)
      .forEach(t => {
        categories[t.category] = (categories[t.category] || 0) + t.amount
      })
    return categories
  })

  // 獲取交易列表
  const fetchTransactions = async (params?: {
    page?: number
    limit?: number
    type?: string
    category?: string
    startDate?: string
    endDate?: string
    search?: string
  }) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await transactionApi.getTransactions(params)
      
      if (params?.page && params.page > 1) {
        // 分頁加載，追加到現有數據
        transactions.value.push(...response.data.data)
      } else {
        // 新查詢，替換數據
        transactions.value = response.data.data
      }
      
      pagination.value = response.data.pagination
      
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取交易列表失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 創建交易
  const createTransaction = async (transactionData: Omit<Transaction, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await transactionApi.createTransaction(transactionData)
      
      // 將新交易添加到列表開頭
      transactions.value.unshift(response.data.transaction)
      
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '創建交易失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 更新交易
  const updateTransaction = async (id: string, updates: Partial<Transaction>) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await transactionApi.updateTransaction(id, updates)
      
      // 更新本地數據
      const index = transactions.value.findIndex(t => t.id === id)
      if (index > -1) {
        transactions.value[index] = response.data.transaction
      }
      
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '更新交易失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 刪除交易
  const deleteTransaction = async (id: string) => {
    try {
      isLoading.value = true
      error.value = null
      
      await transactionApi.deleteTransaction(id)
      
      // 從本地數據中移除
      const index = transactions.value.findIndex(t => t.id === id)
      if (index > -1) {
        transactions.value.splice(index, 1)
      }
      
    } catch (err: any) {
      error.value = err.response?.data?.message || '刪除交易失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 獲取財務統計
  const fetchStats = async (params?: { startDate?: string; endDate?: string }) => {
    try {
      const response = await transactionApi.getStats(params)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取統計失敗'
      throw err
    }
  }

  // 清除錯誤
  const clearError = () => {
    error.value = null
  }

  // 重置狀態
  const reset = () => {
    transactions.value = []
    pagination.value = null
    error.value = null
    isLoading.value = false
  }

  return {
    // 狀態
    transactions,
    pagination,
    isLoading,
    error,
    
    // 計算屬性
    totalIncome,
    totalExpenses,
    balance,
    expenseByCategory,
    incomeByCategory,
    
    // 方法
    fetchTransactions,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    fetchStats,
    clearError,
    reset
  }
})

<template>
  <div class="card">
    <h2 class="text-2xl font-bold text-yellow-400 font-pixel mb-6">角色狀態</h2>
    
    <div v-if="characterStore.isLoading" class="flex justify-center py-8">
      <div class="loading-spinner"></div>
    </div>
    
    <div v-else-if="characterStore.character" class="space-y-6">
      <!-- 基本信息 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="stat-box">
          <div class="text-3xl">⭐</div>
          <div>
            <div class="text-sm text-stone-400">等級</div>
            <div class="text-xl font-bold text-yellow-300">{{ characterStore.level }}</div>
          </div>
        </div>
        
        <div class="stat-box">
          <div class="text-3xl">💎</div>
          <div>
            <div class="text-sm text-stone-400">水晶</div>
            <div class="text-xl font-bold text-blue-400">{{ characterStore.crystals }}</div>
          </div>
        </div>
        
        <div class="stat-box">
          <div class="text-3xl">✨</div>
          <div>
            <div class="text-sm text-stone-400">天賦點</div>
            <div class="text-xl font-bold text-purple-400">{{ characterStore.talentPoints }}</div>
          </div>
        </div>
        
        <div class="stat-box">
          <div class="text-3xl">💰</div>
          <div>
            <div class="text-sm text-stone-400">餘額</div>
            <div class="text-xl font-bold text-green-400">{{ formatCurrency(transactionStore.balance) }}</div>
          </div>
        </div>
      </div>

      <!-- 經驗值條 -->
      <div>
        <div class="flex justify-between text-sm text-stone-400 mb-2">
          <span>經驗值</span>
          <span>{{ characterStore.xp }} / {{ characterStore.xpForNextLevel }}</span>
        </div>
        <div class="w-full bg-stone-700 rounded-full h-3">
          <div 
            class="bg-gradient-to-r from-yellow-500 to-yellow-400 h-3 rounded-full transition-all duration-500"
            :style="{ width: `${(characterStore.xp / characterStore.xpForNextLevel) * 100}%` }"
          ></div>
        </div>
      </div>

      <!-- 屬性 -->
      <div class="grid grid-cols-3 gap-4">
        <div class="text-center">
          <div class="text-2xl mb-1">❤️</div>
          <div class="text-sm text-stone-400">生命值</div>
          <div class="font-bold text-red-400">{{ characterStore.character.stats.maxHp }}</div>
        </div>
        
        <div class="text-center">
          <div class="text-2xl mb-1">⚔️</div>
          <div class="text-sm text-stone-400">攻擊力</div>
          <div class="font-bold text-orange-400">{{ characterStore.totalAttack }}</div>
        </div>
        
        <div class="text-center">
          <div class="text-2xl mb-1">🛡️</div>
          <div class="text-sm text-stone-400">防禦力</div>
          <div class="font-bold text-blue-400">{{ characterStore.totalDefense }}</div>
        </div>
      </div>

      <!-- 裝備 -->
      <div>
        <h3 class="text-lg font-bold text-stone-300 mb-3">當前裝備</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="bg-stone-700/50 p-3 rounded-lg text-center">
            <div class="text-2xl mb-2">⚔️</div>
            <div class="text-sm text-stone-400">武器</div>
            <div class="font-medium">
              {{ getEquipmentName(characterStore.character.equipment.weapon) || '無' }}
            </div>
          </div>
          
          <div class="bg-stone-700/50 p-3 rounded-lg text-center">
            <div class="text-2xl mb-2">🛡️</div>
            <div class="text-sm text-stone-400">護甲</div>
            <div class="font-medium">
              {{ getEquipmentName(characterStore.character.equipment.armor) || '無' }}
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else class="text-center py-8 text-stone-400">
      無法載入角色信息
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCharacterStore } from '@/stores/character'
import { useTransactionStore } from '@/stores/transaction'

const characterStore = useCharacterStore()
const transactionStore = useTransactionStore()

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-TW', {
    style: 'currency',
    currency: 'TWD'
  }).format(amount)
}

const getEquipmentName = (equipmentId: string | null) => {
  if (!equipmentId) return null
  
  // 這裡應該從裝備數據中查找名稱
  // 暫時返回ID
  const equipmentNames: Record<string, string> = {
    'sword_of_avarice': '貪婪之劍',
    'merchants_garb': '商賈之袍'
  }
  
  return equipmentNames[equipmentId] || equipmentId
}
</script>

<style scoped>
.loading-spinner {
  @apply border-4 border-stone-600 border-t-yellow-400 rounded-full w-8 h-8 animate-spin;
}
</style>

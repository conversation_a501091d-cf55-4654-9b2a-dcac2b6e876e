import React, { createContext, useState, useCallback, useContext } from 'react';
import { v4 as uuidv4 } from 'uuid';

export type ToastType = 'success' | 'reward' | 'info' | 'error';

export interface ToastMessage {
    id: string;
    message: string;
    type: ToastType;
}

interface NotificationContextType {
    toasts: ToastMessage[];
    addToast: (message: string, type: ToastType) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [toasts, setToasts] = useState<ToastMessage[]>([]);

    const removeToast = useCallback((id: string) => {
        setToasts(currentToasts => currentToasts.filter(toast => toast.id !== id));
    }, []);

    const addToast = useCallback((message: string, type: ToastType) => {
        const id = uuidv4();
        const newToast: ToastMessage = { id, message, type };
        
        setToasts(currentToasts => [newToast, ...currentToasts]);

        setTimeout(() => {
            removeToast(id);
        }, 4000); // Remove after 4 seconds
    }, [removeToast]);

    return (
        <NotificationContext.Provider value={{ toasts, addToast }}>
            {children}
        </NotificationContext.Provider>
    );
};

export const useNotification = (): NotificationContextType => {
    const context = useContext(NotificationContext);
    if (context === undefined) {
        throw new Error('useNotification must be used within a NotificationProvider');
    }
    return context;
};

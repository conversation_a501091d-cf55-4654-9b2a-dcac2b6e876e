import React, { useEffect, useRef, useState } from 'react';
import { Monster } from '../types';

interface CombatViewProps {
    playerStats: { maxHp: number; attack: number; defense: number; };
    playerHp: number;
    monster: Monster;
    monsterHp: number;
    combatLog: string[];
    isPlayerTurn: boolean;
    onPlayerAction: (action: 'attack' | 'flee') => void;
}

const HealthBar: React.FC<{ current: number; max: number; label: string }> = ({ current, max, label }) => {
    const percentage = max > 0 ? (current / max) * 100 : 0;
    return (
        <div>
            <div className="flex justify-between items-center mb-1 text-sm">
                <span className="font-bold">{label}</span>
                <span className="font-pixel">{current} / {max}</span>
            </div>
            <div className="w-full bg-stone-700 rounded-full h-4 border-2 border-gray-900">
                <div
                    className="bg-red-600 h-full rounded-full transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                ></div>
            </div>
        </div>
    );
};

const CombatView: React.FC<CombatViewProps> = ({
    playerStats, playerHp, monster, monsterHp, combatLog, isPlayerTurn, onPlayerAction
}) => {
    const logContainerRef = useRef<HTMLDivElement>(null);
    const [playerHit, setPlayerHit] = useState(false);
    const [monsterHit, setMonsterHit] = useState(false);

    useEffect(() => {
        if (logContainerRef.current) {
            logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
        }

        if (combatLog.length > 0) {
            const lastMessage = combatLog[combatLog.length - 1];
            if (lastMessage.includes('你攻擊')) {
                setMonsterHit(true);
                setTimeout(() => setMonsterHit(false), 400); // Animation duration matches CSS
            } else if (lastMessage.includes('反擊')) {
                setPlayerHit(true);
                setTimeout(() => setPlayerHit(false), 400); // Animation duration matches CSS
            }
        }
    }, [combatLog]);

    return (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
            <div className="bg-stone-900/90 p-6 rounded-lg border-2 border-yellow-800 shadow-2xl w-full max-w-4xl text-white">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                    {/* Player Side */}
                    <div className="text-center p-4 bg-stone-800/50 rounded-lg">
                        <div className={`text-6xl mb-4 ${playerHit ? 'animate-shake' : ''}`}>🧑‍</div>
                        <h2 className="text-2xl font-bold font-pixel text-blue-400">你</h2>
                        <HealthBar current={playerHp} max={playerStats.maxHp} label="生命值" />
                    </div>

                    {/* Monster Side */}
                    <div className="text-center p-4 bg-stone-800/50 rounded-lg">
                        <div className={`text-6xl mb-4 ${monsterHit ? 'animate-shake' : ''}`}>{monster.icon}</div>
                        <h2 className="text-2xl font-bold font-pixel text-red-400">{monster.name}</h2>
                        <HealthBar current={monsterHp} max={monster.hp} label="生命值" />
                    </div>
                </div>

                {/* Combat Log */}
                <div ref={logContainerRef} className="my-6 bg-stone-900/70 p-4 rounded-md border border-gray-700 h-40 overflow-y-auto">
                    {combatLog.map((entry, index) => (
                        <p key={index} className="text-sm text-gray-300 mb-1">{`> ${entry}`}</p>
                    ))}
                </div>
                
                {/* Action Buttons */}
                <div className="flex justify-center space-x-4">
                    <button
                        onClick={() => onPlayerAction('attack')}
                        disabled={!isPlayerTurn}
                        className="bg-red-700 font-bold py-3 px-8 rounded-lg hover:bg-red-600 transition-colors duration-300 shadow-md border-b-4 border-red-900 disabled:bg-gray-600 disabled:border-gray-800 disabled:cursor-not-allowed"
                    >
                        攻擊
                    </button>
                    <button
                        onClick={() => onPlayerAction('flee')}
                        disabled={!isPlayerTurn}
                        className="bg-gray-500 font-bold py-3 px-8 rounded-lg hover:bg-gray-400 transition-colors duration-300 shadow-md border-b-4 border-gray-700 disabled:bg-gray-600 disabled:border-gray-800 disabled:cursor-not-allowed"
                    >
                        逃跑
                    </button>
                </div>
            </div>
        </div>
    );
};

export default CombatView;
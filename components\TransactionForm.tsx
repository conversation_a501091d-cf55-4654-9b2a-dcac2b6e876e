import React, { useState } from 'react';
import { Transaction, TransactionType, Category } from '../types';
import { CATEGORIES } from '../constants';

interface TransactionFormProps {
    onAddTransaction: (transaction: Omit<Transaction, 'id' | 'date'>) => void;
}

const TransactionForm: React.FC<TransactionFormProps> = ({ onAddTransaction }) => {
    const [description, setDescription] = useState('');
    const [amount, setAmount] = useState('');
    const [type, setType] = useState<TransactionType>(TransactionType.EXPENSE);
    const [category, setCategory] = useState<Category>('補給品');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const numericAmount = parseFloat(amount);
        if (!description || isNaN(numericAmount) || numericAmount <= 0) {
            alert('請填寫所有欄位並輸入有效的數值。');
            return;
        }

        onAddTransaction({
            description,
            amount: numericAmount,
            type,
            category,
        });

        // Reset form
        setDescription('');
        setAmount('');
        setType(TransactionType.EXPENSE);
        setCategory('補給品');
    };

    const inputClasses = "w-full bg-stone-900/70 border-2 border-gray-600 rounded-md py-2 px-3 focus:outline-none focus:border-yellow-500 transition-colors";
    const labelClasses = "block text-sm font-bold mb-1 text-gray-300";

    return (
        <div className="bg-stone-800/80 p-6 rounded-lg border-2 border-yellow-800 shadow-lg">
            <h2 className="text-2xl font-bold mb-4 text-yellow-400 font-pixel">記錄新項目</h2>
            <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                    <label htmlFor="description" className={labelClasses}>描述</label>
                    <input
                        id="description"
                        type="text"
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        placeholder="例如：龍牙"
                        className={inputClasses}
                        required
                    />
                </div>
                <div>
                    <label htmlFor="amount" className={labelClasses}>金額 (金幣)</label>
                    <input
                        id="amount"
                        type="number"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        placeholder="例如：100"
                        className={inputClasses}
                        required
                    />
                </div>
                <div>
                     <label className={labelClasses}>類型</label>
                    <div className="flex space-x-2">
                        <button
                            type="button"
                            onClick={() => setType(TransactionType.INCOME)}
                            className={`flex-1 py-2 rounded-md transition-colors ${type === TransactionType.INCOME ? 'bg-green-600 border-b-4 border-green-800' : 'bg-stone-700 hover:bg-stone-600'}`}
                        >
                            收入
                        </button>
                        <button
                            type="button"
                            onClick={() => setType(TransactionType.EXPENSE)}
                            className={`flex-1 py-2 rounded-md transition-colors ${type === TransactionType.EXPENSE ? 'bg-red-600 border-b-4 border-red-800' : 'bg-stone-700 hover:bg-stone-600'}`}
                        >
                            支出
                        </button>
                    </div>
                </div>
                <div>
                    <label htmlFor="category" className={labelClasses}>類別</label>
                    <select
                        id="category"
                        value={category}
                        onChange={(e) => setCategory(e.target.value as Category)}
                        className={inputClasses}
                    >
                        {CATEGORIES.map(cat => (
                            <option key={cat} value={cat}>{cat}</option>
                        ))}
                    </select>
                </div>
                <button
                    type="submit"
                    className="w-full bg-yellow-600 text-stone-900 font-bold py-3 px-4 rounded-lg hover:bg-yellow-500 transition-colors duration-300 shadow-md border-b-4 border-yellow-800 hover:border-yellow-700 active:border-b-0 active:translate-y-1"
                >
                    添加到帳本
                </button>
            </form>
        </div>
    );
};

export default TransactionForm;
import React from 'react';
import { Quest } from '../types';

interface QuestBoardProps {
    quests: Quest[];
}

const QuestBoard: React.FC<QuestBoardProps> = ({ quests }) => {
    return (
        <div className="bg-stone-800/80 p-6 rounded-lg border-2 border-yellow-800 shadow-lg">
            <h2 className="text-2xl font-bold mb-4 text-yellow-400 font-pixel">任務看板</h2>
            <ul className="space-y-3 max-h-[250px] overflow-y-auto pr-2">
                {quests.map(quest => (
                    <li key={quest.id} className={`p-3 rounded-md transition-all ${quest.isCompleted ? 'bg-green-900/50 opacity-60' : 'bg-stone-900/50'}`}>
                        <div className="flex justify-between items-center">
                            <div>
                                <h3 className={`font-bold ${quest.isCompleted ? 'text-gray-400' : 'text-white'}`}>{quest.title}</h3>
                                <p className="text-sm text-gray-400">{quest.description}</p>
                            </div>
                            <div className="text-right flex-shrink-0 ml-4">
                                <p className={`font-pixel ${quest.isCompleted ? 'text-gray-500' : 'text-blue-400'}`}>{quest.reward} 💎</p>
                                {quest.isCompleted ? (
                                     <span className="text-xs text-green-400 font-bold">已完成</span>
                                ) : (
                                    <span className="text-xs text-gray-500">進行中</span>
                                )}
                            </div>
                        </div>
                    </li>
                ))}
            </ul>
        </div>
    );
};

export default QuestBoard;

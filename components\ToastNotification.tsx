import React from 'react';
import { useNotification, ToastType } from '../contexts/NotificationContext';

const ToastIcon: React.FC<{ type: ToastType }> = ({ type }) => {
    switch (type) {
        case 'success':
            return <span className="text-2xl">✔️</span>;
        case 'reward':
            return <span className="text-2xl">✨</span>;
        case 'info':
            return <span className="text-2xl">ℹ️</span>;
        case 'error':
            return <span className="text-2xl">❌</span>;
        default:
            return null;
    }
};

const ToastNotification: React.FC = () => {
    const { toasts } = useNotification();

    const getToastColors = (type: ToastType) => {
        switch (type) {
            case 'success':
                return 'bg-green-800/90 border-green-500';
            case 'reward':
                return 'bg-purple-800/90 border-purple-500';
            case 'info':
                return 'bg-blue-800/90 border-blue-500';
            case 'error':
                return 'bg-red-800/90 border-red-500';
            default:
                return 'bg-stone-800/90 border-stone-500';
        }
    };

    return (
        <div className="fixed top-5 right-5 z-[100] w-full max-w-sm space-y-3">
            {toasts.map(toast => (
                <div
                    key={toast.id}
                    className={`toast-item flex items-center p-3 rounded-lg shadow-lg border-l-4 ${getToastColors(toast.type)}`}
                >
                    <ToastIcon type={toast.type} />
                    <p className="ml-3 font-semibold text-stone-100">{toast.message}</p>
                </div>
            )).reverse()}
        </div>
    );
};

export default ToastNotification;

<!DOCTYPE html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RPG冒險家帳本</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=MedievalSharp&family=Press+Start+2P&display=swap" rel="stylesheet">
    
    <style>
      body {
        font-family: 'MedievalSharp', cursive;
        background: linear-gradient(135deg, #1c1917 0%, #292524 100%);
        min-height: 100vh;
      }
      
      .font-pixel {
        font-family: 'Press Start 2P', cursive;
      }
      
      /* 自定義滾動條 */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #44403c;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #78716c;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #a8a29e;
      }
      
      /* 載入動畫 */
      .loading-spinner {
        border: 4px solid #44403c;
        border-top: 4px solid #fbbf24;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* 奇幻風格按鈕 */
      .fantasy-button {
        background: linear-gradient(145deg, #fbbf24, #f59e0b);
        border: 2px solid #d97706;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
      }
      
      .fantasy-button:hover {
        background: linear-gradient(145deg, #f59e0b, #d97706);
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
      }
      
      .fantasy-button:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }
    </style>
  </head>
  <body class="bg-stone-900 text-stone-100">
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>

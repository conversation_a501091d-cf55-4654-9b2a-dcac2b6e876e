{"name": "rpg-bookkeep-backend", "version": "1.0.0", "description": "RPG冒險家帳本後端API", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "@google/generative-ai": "^0.2.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "keywords": ["rpg", "bookkeeping", "finance", "game"], "author": "Your Name", "license": "MIT"}
<template>
  <div class="card">
    <h2 class="text-2xl font-bold text-yellow-400 font-pixel mb-6">任務板</h2>
    
    <div class="space-y-4">
      <div 
        v-for="quest in sampleQuests" 
        :key="quest.id"
        :class="[
          'p-4 rounded-lg border-2 transition-colors',
          quest.isCompleted 
            ? 'border-green-500 bg-green-500/10' 
            : 'border-stone-600 bg-stone-700/50'
        ]"
      >
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div class="flex items-center space-x-2 mb-2">
              <span class="text-2xl">{{ quest.isCompleted ? '✅' : '📋' }}</span>
              <h3 class="font-bold text-stone-200">{{ quest.title }}</h3>
            </div>
            <p class="text-sm text-stone-400 mb-3">{{ quest.description }}</p>
            
            <!-- 進度條 -->
            <div v-if="quest.progress !== undefined" class="mb-3">
              <div class="flex justify-between text-xs text-stone-400 mb-1">
                <span>進度</span>
                <span>{{ quest.progress }} / {{ quest.target }}</span>
              </div>
              <div class="w-full bg-stone-600 rounded-full h-2">
                <div 
                  class="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${Math.min((quest.progress / quest.target) * 100, 100)}%` }"
                ></div>
              </div>
            </div>
          </div>
          
          <div class="ml-4 text-right">
            <div class="flex items-center space-x-1 text-blue-400">
              <span class="text-lg">💎</span>
              <span class="font-bold">{{ quest.reward }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div v-if="sampleQuests.length === 0" class="text-center py-8 text-stone-400">
        目前沒有可用的任務
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 暫時使用範例任務數據
const sampleQuests = ref([
  {
    id: 'earn_first_gold',
    title: '第一次收入',
    description: '記錄你的第一筆收入，開始你的財富冒險！',
    isCompleted: false,
    reward: 10,
    progress: 0,
    target: 1
  },
  {
    id: 'spend_wisely',
    title: '明智消費',
    description: '記錄5筆支出交易，學會管理你的開銷。',
    isCompleted: false,
    reward: 15,
    progress: 2,
    target: 5
  },
  {
    id: 'save_money',
    title: '儲蓄達人',
    description: '累積淨餘額達到 $10,000。',
    isCompleted: false,
    reward: 25,
    progress: 3500,
    target: 10000
  },
  {
    id: 'categorize_expenses',
    title: '分類專家',
    description: '在至少3個不同分類中記錄交易。',
    isCompleted: true,
    reward: 20
  }
])
</script>

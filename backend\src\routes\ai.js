import express from 'express';
import { body, validationResult } from 'express-validator';
import { GoogleGenerativeAI } from '@google/generative-ai';
import Transaction from '../models/Transaction.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要認證
router.use(authenticateToken);

// 初始化 Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// 獲取財務建議
router.post('/financial-advice', async (req, res) => {
  try {
    if (!process.env.GEMINI_API_KEY) {
      return res.status(500).json({
        message: 'AI 服務未配置'
      });
    }

    // 獲取用戶的財務數據
    const transactions = await Transaction.find({ userId: req.user._id })
      .sort({ createdAt: -1 })
      .limit(100); // 最近100筆交易

    // 計算財務統計
    let totalIncome = 0;
    let totalExpenses = 0;
    const expenseByCategory = {};

    transactions.forEach(t => {
      if (t.type === 'income') {
        totalIncome += t.amount;
      } else {
        totalExpenses += t.amount;
        expenseByCategory[t.category] = (expenseByCategory[t.category] || 0) + t.amount;
      }
    });

    const balance = totalIncome - totalExpenses;

    // 構建支出詳情
    const expenseDetails = Object.entries(expenseByCategory)
      .map(([category, amount]) => `- ${category}: ${amount} 金幣`)
      .join('\n');

    const systemInstruction = `你是一位奇幻RPG世界中的年邁智者，為一位冒險家提供財務建議。
你的語氣應該是智慧、鼓勵且充滿主題性的。請使用奇幻術語，如「金幣」、「任務」、「冒險」、「裝備」、「藥水」等。
請不要脫離你的角色。`;

    const userPrompt = `這是冒險家的財務帳本摘要：
- 總賺取金幣： ${totalIncome}
- 總花費金幣： ${totalExpenses}
- 目前持有金幣： ${balance}
- 支出細目：
${expenseDetails || '- 尚未記錄任何支出。'}

根據以上資訊，請提供簡潔且可行的建議。專注於一兩個關鍵領域。
例如，如果「補給品」花費很高，建議在任務前明智地囤貨。如果收入低，建議接受更多有利可圖的懸賞任務。如果結餘健康，讚揚他們的理財紀律。

開始您的忠告吧，偉大的智者！`;

    try {
      const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
      
      const result = await model.generateContent({
        contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
        generationConfig: {
          temperature: 0.7,
          topP: 1,
          topK: 1,
          maxOutputTokens: 500,
        },
        systemInstruction: {
          parts: [{ text: systemInstruction }]
        }
      });

      const response = await result.response;
      const advice = response.text() || "此刻，空靈之風未帶來任何智慧。未來尚不明朗。";

      res.json({
        advice,
        summary: {
          totalIncome,
          totalExpenses,
          balance,
          expenseByCategory
        }
      });

    } catch (aiError) {
      console.error('AI 服務錯誤:', aiError);
      res.status(500).json({
        message: '與靈界的連接已斷開。',
        fallbackAdvice: generateFallbackAdvice(balance, totalIncome, totalExpenses, expenseByCategory)
      });
    }

  } catch (error) {
    console.error('獲取財務建議錯誤:', error);
    res.status(500).json({
      message: '獲取財務建議失敗'
    });
  }
});

// 生成備用建議（當AI服務不可用時）
function generateFallbackAdvice(balance, income, expenses, expenseByCategory) {
  const advice = [];

  if (balance < 0) {
    advice.push("年輕的冒險家，你的金庫告急！建議減少不必要的開支，專注於高收益的任務。");
  } else if (balance > income * 0.5) {
    advice.push("做得很好！你的理財紀律值得讚揚。考慮投資一些能提升收入的裝備或建築。");
  }

  // 檢查最大支出類別
  const maxExpenseCategory = Object.entries(expenseByCategory)
    .sort(([,a], [,b]) => b - a)[0];

  if (maxExpenseCategory) {
    const [category, amount] = maxExpenseCategory;
    if (amount > expenses * 0.3) {
      advice.push(`注意到你在「${category}」上花費較多，建議審視是否有優化空間。`);
    }
  }

  return advice.join(' ') || "繼續你的冒險之路，智慧地管理你的財富！";
}

export default router;

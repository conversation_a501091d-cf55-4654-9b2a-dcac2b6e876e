import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由組件
const Login = () => import('@/views/Login.vue')
const Register = () => import('@/views/Register.vue')
const Dashboard = () => import('@/views/Dashboard.vue')
const Character = () => import('@/views/Character.vue')
const Shop = () => import('@/views/Shop.vue')
const Talents = () => import('@/views/Talents.vue')
const Town = () => import('@/views/Town.vue')
const Exploration = () => import('@/views/Exploration.vue')
const Profile = () => import('@/views/Profile.vue')

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { requiresGuest: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/character',
    name: 'Character',
    component: Character,
    meta: { requiresAuth: true }
  },
  {
    path: '/shop',
    name: 'Shop',
    component: Shop,
    meta: { requiresAuth: true }
  },
  {
    path: '/talents',
    name: 'Talents',
    component: Talents,
    meta: { requiresAuth: true }
  },
  {
    path: '/town',
    name: 'Town',
    component: Town,
    meta: { requiresAuth: true }
  },
  {
    path: '/exploration',
    name: 'Exploration',
    component: Exploration,
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { requiresAuth: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守衛
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 檢查是否需要認證
  if (to.meta.requiresAuth) {
    if (!authStore.isAuthenticated) {
      // 嘗試從本地存儲恢復認證狀態
      const token = localStorage.getItem('auth_token')
      if (token) {
        try {
          await authStore.verifyToken()
          next()
        } catch (error) {
          next('/login')
        }
      } else {
        next('/login')
      }
    } else {
      next()
    }
  }
  // 檢查是否需要訪客狀態（已登入用戶不能訪問登入/註冊頁面）
  else if (to.meta.requiresGuest) {
    if (authStore.isAuthenticated) {
      next('/dashboard')
    } else {
      next()
    }
  }
  // 其他情況直接通過
  else {
    next()
  }
})

export default router

<template>
  <div class="p-4 md:p-8">
    <h1 class="text-3xl font-bold text-yellow-400 font-pixel mb-8">角色面板</h1>
    
    <div v-if="characterStore.isLoading" class="flex justify-center py-16">
      <div class="loading-spinner"></div>
    </div>
    
    <div v-else-if="characterStore.character" class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 角色狀態 -->
      <CharacterStatusCard />
      
      <!-- 裝備管理 -->
      <div class="card">
        <h2 class="text-2xl font-bold text-yellow-400 font-pixel mb-6">裝備管理</h2>
        <div class="text-center text-stone-400 py-8">
          裝備管理功能開發中...
        </div>
      </div>
    </div>
    
    <div v-else class="text-center py-16 text-stone-400">
      無法載入角色信息
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useCharacterStore } from '@/stores/character'
import CharacterStatusCard from '@/components/CharacterStatusCard.vue'

const characterStore = useCharacterStore()

onMounted(async () => {
  if (!characterStore.character) {
    await characterStore.fetchCharacter()
  }
})
</script>

<style scoped>
.loading-spinner {
  @apply border-4 border-stone-600 border-t-yellow-400 rounded-full w-12 h-12 animate-spin;
}
</style>

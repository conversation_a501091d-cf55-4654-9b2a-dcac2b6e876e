import React from 'react';
import { Building } from '../types';
import { BUILDINGS } from '../constants';

interface TownManagementProps {
    crystals: number;
    level: number;
    builtBuildings: string[];
    uncollectedTaxes: number;
    onBuildBuilding: (building: Building) => void;
    onCollectTaxes: () => void;
    talentBonuses: {
        buildingCostReduction: number;
        taxRateIncrease: number;
    };
}

const TownManagement: React.FC<TownManagementProps> = ({
    crystals,
    level,
    builtBuildings,
    uncollectedTaxes,
    onBuildBuilding,
    onCollectTaxes,
    talentBonuses,
}) => {
    const marketBuilt = builtBuildings.includes('market');
    const baseTaxes = Math.floor(uncollectedTaxes);
    const finalTaxes = Math.ceil(baseTaxes * (1 + talentBonuses.taxRateIncrease));


    return (
        <div className="space-y-8">
            <div className="bg-stone-800/80 p-6 rounded-lg border-2 border-yellow-800 shadow-lg">
                <h2 className="text-2xl font-bold mb-4 text-yellow-400 font-pixel">城鎮管理</h2>
                
                {marketBuilt && (
                    <div className="bg-stone-900/50 p-4 rounded-lg flex items-center justify-between mb-6 border border-gray-700">
                        <div>
                            <h3 className="text-lg font-bold text-yellow-300">未收稅金</h3>
                            <p className="text-2xl font-pixel text-blue-400">{finalTaxes} 💎</p>
                            {talentBonuses.taxRateIncrease > 0 && <p className="text-xs text-green-400">({baseTaxes} + {finalTaxes-baseTaxes} 天賦加成)</p>}
                        </div>
                        <button
                            onClick={onCollectTaxes}
                            disabled={baseTaxes < 1}
                            className="bg-yellow-600 text-stone-900 font-bold py-3 px-5 rounded-lg hover:bg-yellow-500 transition-colors duration-300 shadow-md border-b-4 border-yellow-800 disabled:bg-gray-600 disabled:border-gray-800 disabled:cursor-not-allowed"
                        >
                            徵收稅金
                        </button>
                    </div>
                )}
                
                <h3 className="text-xl font-bold mb-4 text-yellow-400 font-pixel">建築藍圖</h3>
                <ul className="space-y-3 max-h-[400px] overflow-y-auto pr-2">
                   {BUILDINGS.map(building => {
                       const isBuilt = builtBuildings.includes(building.id);
                       const finalCost = Math.ceil(building.cost * (1- talentBonuses.buildingCostReduction));
                       const canAfford = crystals >= finalCost;
                       const meetsLevel = level >= building.requiredLevel;
                       const canBuild = !isBuilt && canAfford && meetsLevel;

                       let buttonText: React.ReactNode = <span>{finalCost} 💎</span>;
                       if (talentBonuses.buildingCostReduction > 0 && !isBuilt) {
                           buttonText = <span className="text-green-300">{finalCost} 💎 <s className="text-red-400/50">{building.cost}</s></span>
                       }

                       if (isBuilt) buttonText = "已建造";
                       else if (!meetsLevel) buttonText = `需要等級 ${building.requiredLevel}`;
                       
                       return (
                            <li key={building.id} className="bg-stone-900/50 p-3 rounded-md flex items-center justify-between">
                                <div className="flex items-center space-x-4">
                                    <span className="text-4xl">{building.id === 'market' ? '📈' : '🔨'}</span>
                                    <div>
                                        <h4 className="font-bold text-white">{building.name}</h4>
                                        <p className="text-sm text-gray-400">{building.description}</p>
                                    </div>
                                </div>
                                <div className="text-right">
                                    <button
                                        onClick={() => onBuildBuilding(building)}
                                        disabled={!canBuild}
                                        className="bg-green-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-green-500 transition-colors duration-300 shadow-md border-b-4 border-green-800 disabled:bg-gray-600 disabled:border-gray-800 disabled:cursor-not-allowed"
                                    >
                                        {buttonText}
                                    </button>
                                </div>
                            </li>
                       );
                    })}
                </ul>

            </div>
        </div>
    );
};

export default TownManagement;
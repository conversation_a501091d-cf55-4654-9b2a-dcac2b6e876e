<template>
  <div class="card">
    <h2 class="text-xl font-bold text-yellow-400 font-pixel mb-4">記錄交易</h2>
    
    <form @submit.prevent="handleSubmit" class="space-y-4">
      <!-- 交易類型 -->
      <div>
        <label class="block text-sm font-medium text-stone-300 mb-2">
          交易類型
        </label>
        <div class="grid grid-cols-2 gap-2">
          <button
            type="button"
            @click="form.type = TransactionType.INCOME"
            :class="[
              'p-3 rounded-lg border-2 transition-colors',
              form.type === TransactionType.INCOME
                ? 'border-green-500 bg-green-500/20 text-green-300'
                : 'border-stone-600 bg-stone-700 text-stone-300 hover:border-stone-500'
            ]"
          >
            💰 收入
          </button>
          <button
            type="button"
            @click="form.type = TransactionType.EXPENSE"
            :class="[
              'p-3 rounded-lg border-2 transition-colors',
              form.type === TransactionType.EXPENSE
                ? 'border-red-500 bg-red-500/20 text-red-300'
                : 'border-stone-600 bg-stone-700 text-stone-300 hover:border-stone-500'
            ]"
          >
            💸 支出
          </button>
        </div>
      </div>

      <!-- 金額 -->
      <div>
        <label for="amount" class="block text-sm font-medium text-stone-300 mb-2">
          金額
        </label>
        <input
          id="amount"
          v-model.number="form.amount"
          type="number"
          min="0"
          step="0.01"
          required
          class="input-field"
          placeholder="請輸入金額"
        />
      </div>

      <!-- 分類 -->
      <div>
        <label for="category" class="block text-sm font-medium text-stone-300 mb-2">
          分類
        </label>
        <select
          id="category"
          v-model="form.category"
          required
          class="input-field"
        >
          <option value="">請選擇分類</option>
          <option 
            v-for="category in availableCategories" 
            :key="category.value" 
            :value="category.value"
          >
            {{ category.icon }} {{ category.label }}
          </option>
        </select>
      </div>

      <!-- 描述 -->
      <div>
        <label for="description" class="block text-sm font-medium text-stone-300 mb-2">
          描述
        </label>
        <input
          id="description"
          v-model="form.description"
          type="text"
          required
          class="input-field"
          placeholder="請輸入交易描述"
        />
      </div>

      <!-- 日期 -->
      <div>
        <label for="date" class="block text-sm font-medium text-stone-300 mb-2">
          日期
        </label>
        <input
          id="date"
          v-model="form.date"
          type="date"
          required
          class="input-field"
        />
      </div>

      <!-- 提交按鈕 -->
      <button
        type="submit"
        :disabled="isLoading"
        class="w-full btn-primary flex items-center justify-center space-x-2"
      >
        <div v-if="isLoading" class="loading-spinner"></div>
        <span>{{ isLoading ? '記錄中...' : '記錄交易' }}</span>
      </button>
    </form>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, ref } from 'vue'
import { useTransactionStore } from '@/stores/transaction'
import { useToastStore } from '@/stores/toast'
import { TransactionType, Category } from '@/types'

const emit = defineEmits<{
  'transaction-added': []
}>()

const transactionStore = useTransactionStore()
const toastStore = useToastStore()

const isLoading = ref(false)

const form = reactive({
  type: TransactionType.EXPENSE,
  amount: 0,
  category: '' as Category | '',
  description: '',
  date: new Date().toISOString().split('T')[0]
})

const availableCategories = computed(() => {
  const incomeCategories = [
    { value: Category.SALARY, label: '薪資', icon: '💼' },
    { value: Category.INVESTMENT, label: '投資', icon: '📈' },
    { value: Category.BUSINESS, label: '營業', icon: '🏪' },
    { value: Category.OTHER_INCOME, label: '其他收入', icon: '💰' }
  ]

  const expenseCategories = [
    { value: Category.FOOD, label: '餐飲', icon: '🍽️' },
    { value: Category.TRANSPORT, label: '交通', icon: '🚗' },
    { value: Category.ENTERTAINMENT, label: '娛樂', icon: '🎮' },
    { value: Category.SHOPPING, label: '購物', icon: '🛒' },
    { value: Category.BILLS, label: '帳單', icon: '📄' },
    { value: Category.HEALTHCARE, label: '醫療', icon: '🏥' },
    { value: Category.EDUCATION, label: '教育', icon: '📚' },
    { value: Category.OTHER_EXPENSE, label: '其他支出', icon: '💸' }
  ]

  return form.type === TransactionType.INCOME ? incomeCategories : expenseCategories
})

const handleSubmit = async () => {
  if (!form.category) {
    toastStore.error('請選擇分類')
    return
  }

  try {
    isLoading.value = true
    
    await transactionStore.createTransaction({
      type: form.type,
      amount: form.amount,
      category: form.category as Category,
      description: form.description,
      date: form.date
    })

    toastStore.success('交易記錄成功！')
    
    // 重置表單
    form.amount = 0
    form.category = ''
    form.description = ''
    form.date = new Date().toISOString().split('T')[0]
    
    emit('transaction-added')
  } catch (error) {
    toastStore.error('記錄交易失敗')
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.loading-spinner {
  @apply border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin;
}
</style>

{"name": "rpg-bookkeep-frontend", "version": "1.0.0", "description": "RPG冒險家帳本前端", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "recharts": "^2.8.0", "vue-chartjs": "^5.3.0", "chart.js": "^4.4.0", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "date-fns": "^2.30.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.10.4", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "typescript": "~5.3.0", "vite": "^5.0.8", "vue-tsc": "^1.8.25"}}
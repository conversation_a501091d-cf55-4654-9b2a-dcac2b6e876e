import mongoose from 'mongoose';

const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      dbName: process.env.DB_NAME || 'rpg-bookkeep'
    });

    console.log(`🍃 MongoDB 連接成功: ${conn.connection.host}`);
    
    // 監聽連接事件
    mongoose.connection.on('error', (err) => {
      console.error('MongoDB 連接錯誤:', err);
    });

    mongoose.connection.on('disconnected', () => {
      console.log('MongoDB 連接已斷開');
    });

    // 優雅關閉
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      console.log('MongoDB 連接已關閉');
      process.exit(0);
    });

  } catch (error) {
    console.error('MongoDB 連接失敗:', error.message);
    process.exit(1);
  }
};

export default connectDB;

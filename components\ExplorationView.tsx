
import React, { useState } from 'react';
import { Monster } from '../types';
import { LOCATIONS, MONSTERS, MONSTER_PARTS } from '../constants';

interface ExplorationViewProps {
    level: number;
    crystals: number;
    onHuntMonster: (monster: Monster) => void;
}

const ExplorationView: React.FC<ExplorationViewProps> = ({ level, crystals, onHuntMonster }) => {
    const [selectedLocationId, setSelectedLocationId] = useState<string | null>(LOCATIONS[0]?.id || null);
    
    const unlockedLocations = LOCATIONS.filter(loc => level >= loc.requiredLevel);
    const selectedLocation = LOCATIONS.find(loc => loc.id === selectedLocationId);
    
    return (
        <div className="bg-stone-800/80 p-6 rounded-lg border-2 border-yellow-800 shadow-lg">
            <h2 className="text-2xl font-bold mb-4 text-yellow-400 font-pixel">探索世界</h2>
            
            <div className="mb-6">
                <h3 className="text-lg font-bold text-yellow-300 mb-2">選擇地點</h3>
                <div className="flex flex-wrap gap-2">
                    {LOCATIONS.map(loc => {
                        const isUnlocked = level >= loc.requiredLevel;
                        return (
                            <button
                                key={loc.id}
                                disabled={!isUnlocked}
                                onClick={() => setSelectedLocationId(loc.id)}
                                className={`px-4 py-2 rounded-md transition-colors text-sm font-bold ${
                                    selectedLocationId === loc.id 
                                        ? 'bg-yellow-600 text-stone-900' 
                                        : isUnlocked
                                        ? 'bg-stone-700 hover:bg-stone-600'
                                        : 'bg-stone-900 text-gray-600 cursor-not-allowed'
                                }`}
                            >
                                {loc.name} {isUnlocked ? '' : `(需要等級 ${loc.requiredLevel})`}
                            </button>
                        );
                    })}
                </div>
            </div>

            {selectedLocation && (
                <div>
                    <h3 className="text-xl font-bold mb-4 text-yellow-400 font-pixel">地點: {selectedLocation.name}</h3>
                    <ul className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
                        {selectedLocation.monsters.map(monsterId => {
                            const monster = MONSTERS.find(m => m.id === monsterId);
                            if (!monster) return null;
                            
                            const canHunt = level >= monster.level && crystals >= monster.huntCost;

                            return (
                                <li key={monster.id} className="bg-stone-900/50 p-4 rounded-lg border border-gray-700">
                                    <div className="flex justify-between items-start">
                                        <div className="flex items-center space-x-4">
                                            <span className="text-5xl">{monster.icon}</span>
                                            <div>
                                                <h4 className="text-lg font-bold text-white">{monster.name}</h4>
                                                <p className="text-xs text-gray-400">等級: {monster.level} | 經驗: {monster.xp}</p>
                                                <div className="mt-2">
                                                    <h5 className="text-xs font-bold text-yellow-200">可能掉落:</h5>
                                                    <div className="flex flex-wrap gap-x-3 gap-y-1 text-xs text-gray-300">
                                                        {monster.drops.map(drop => {
                                                            const part = MONSTER_PARTS.find(p => p.id === drop.partId);
                                                            return part ? <span key={part.id}>{part.icon} {part.name}</span> : null;
                                                        })}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="text-right flex-shrink-0">
                                            <button
                                                onClick={() => onHuntMonster(monster)}
                                                disabled={!canHunt}
                                                className="bg-red-700 text-white font-bold py-2 px-4 rounded-lg hover:bg-red-600 transition-colors duration-300 shadow-md border-b-4 border-red-900 disabled:bg-gray-600 disabled:border-gray-800 disabled:cursor-not-allowed"
                                            >
                                                狩獵 ({monster.huntCost} 💎)
                                            </button>
                                        </div>
                                    </div>
                                </li>
                            );
                        })}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default ExplorationView;

import { GoogleGenAI } from "@google/genai";

interface FinancialSummary {
    income: number;
    expenses: number;
    balance: number;
    expenseByCategory: Record<string, number>;
}

const ai = new GoogleGenAI({ apiKey: process.env.API_KEY });

export async function getFinancialAdvice(summary: FinancialSummary): Promise<string> {
    const expenseDetails = Object.entries(summary.expenseByCategory)
        .map(([category, amount]) => `- ${category}: ${amount} 金幣`)
        .join('\n');

    const systemInstruction = `你是一位奇幻RPG世界中的年邁智者，為一位冒險家提供財務建議。
你的語氣應該是智慧、鼓勵且充滿主題性的。請使用奇幻術語，如「金幣」、「任務」、「冒險」、「裝備」、「藥水」等。
請不要脫離你的角色。`;

    const userPrompt = `這是冒險家的財務帳本摘要：
- 總賺取金幣： ${summary.income}
- 總花費金幣： ${summary.expenses}
- 目前持有金幣： ${summary.balance}
- 支出細目：
${expenseDetails || '- 尚未記錄任何支出。'}

根據以上資訊，請提供簡潔且可行的建議。專注於一兩個關鍵領域。
例如，如果「補給品」花費很高，建議在任務前明智地囤貨。如果收入低，建議接受更多有利可圖的懸賞任務。如果結餘健康，讚揚他們的理財紀律。

開始您的忠告吧，偉大的智者！
`;

    try {
        const response = await ai.models.generateContent({
            model: 'gemini-2.5-flash',
            contents: userPrompt,
            config: {
                systemInstruction,
                temperature: 0.7,
                topP: 1,
                topK: 1,
            }
        });

        // According to documentation, response.text is the most direct way to get the text.
        // Return a fallback message if the response text is empty for a better user experience.
        return response.text || "此刻，空靈之風未帶來任何智慧。未來尚不明朗。";
       
    } catch (error) {
        console.error("Error fetching advice from Gemini API:", error);
        throw new Error("與靈界的連接已斷開。");
    }
}

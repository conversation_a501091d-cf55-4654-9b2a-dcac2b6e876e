import React from 'react';
import { EquipmentItem } from '../types';
import { EQUIPMENT_ITEMS } from '../constants';

interface EquipmentShopProps {
    crystals: number;
    ownedEquipment: string[];
    onPurchaseItem: (item: EquipmentItem) => void;
    talentBonuses: {
        shopCostReduction: number;
    };
}

const EquipmentShop: React.FC<EquipmentShopProps> = ({ crystals, ownedEquipment, onPurchaseItem, talentBonuses }) => {
    return (
        <div className="bg-stone-800/80 p-6 rounded-lg border-2 border-yellow-800 shadow-lg">
            <h2 className="text-2xl font-bold mb-4 text-yellow-400 font-pixel">裝備商店</h2>
            <div className="mb-4 text-right text-lg font-pixel text-blue-400">你擁有: {crystals} 💎</div>
            <ul className="space-y-3 max-h-[400px] overflow-y-auto pr-2">
                {EQUIPMENT_ITEMS.map(item => {
                    const finalCost = Math.ceil(item.cost * (1 - talentBonuses.shopCostReduction));
                    const canAfford = crystals >= finalCost;

                    return (
                        <li key={item.id} className="bg-stone-900/50 p-3 rounded-md flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                                <span className="text-3xl">{item.icon}</span>
                                <div>
                                    <h3 className="font-bold text-white">{item.name}</h3>
                                    <p className="text-sm text-green-400">{item.bonus.description}</p>
                                </div>
                            </div>
                            <div className="text-right">
                                {ownedEquipment.includes(item.id) ? (
                                    <span className="text-sm font-bold text-gray-500">已擁有</span>
                                ) : (
                                    <button
                                        onClick={() => onPurchaseItem(item)}
                                        disabled={!canAfford}
                                        className="bg-yellow-600 text-stone-900 font-bold py-2 px-4 rounded-lg hover:bg-yellow-500 transition-colors duration-300 shadow-md border-b-4 border-yellow-800 disabled:bg-gray-600 disabled:border-gray-800 disabled:cursor-not-allowed"
                                    >
                                        {talentBonuses.shopCostReduction > 0 ? (
                                            <span className="text-green-300">{finalCost} 💎 <s className="text-red-400/50">{item.cost}</s></span>
                                        ) : (
                                            `${item.cost} 💎`
                                        )}
                                    </button>
                                )}
                            </div>
                        </li>
                    )
                })}
            </ul>
        </div>
    );
};

export default EquipmentShop;
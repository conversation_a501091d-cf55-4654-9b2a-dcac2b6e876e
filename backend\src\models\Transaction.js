import mongoose from 'mongoose';

const transactionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  description: {
    type: String,
    required: [true, '交易描述是必需的'],
    trim: true,
    maxlength: [200, '描述不能超過200個字符']
  },
  amount: {
    type: Number,
    required: [true, '金額是必需的'],
    min: [0.01, '金額必須大於0']
  },
  type: {
    type: String,
    required: true,
    enum: {
      values: ['income', 'expense'],
      message: '交易類型必須是 income 或 expense'
    }
  },
  category: {
    type: String,
    required: [true, '類別是必需的'],
    enum: {
      values: [
        '薪水', '任務獎勵', '稅金', // 收入類別
        '補給品', '裝備', '建設', '餐飲', '交通', '娛樂', '狩獵', '其他' // 支出類別
      ],
      message: '無效的交易類別'
    }
  },
  tags: [{
    type: String,
    trim: true
  }],
  location: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    maxlength: [500, '備註不能超過500個字符']
  },
  attachments: [{
    filename: String,
    url: String,
    size: Number
  }],
  isRecurring: {
    type: Boolean,
    default: false
  },
  recurringPattern: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'yearly']
    },
    interval: {
      type: Number,
      min: 1
    },
    endDate: Date
  },
  relatedQuest: {
    type: String // questId
  },
  xpAwarded: {
    type: Number,
    default: 0
  },
  crystalsAwarded: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引優化
transactionSchema.index({ userId: 1, createdAt: -1 });
transactionSchema.index({ userId: 1, type: 1 });
transactionSchema.index({ userId: 1, category: 1 });
transactionSchema.index({ createdAt: -1 });

// 虛擬字段：格式化日期
transactionSchema.virtual('formattedDate').get(function() {
  return this.createdAt.toLocaleDateString('zh-TW');
});

// 虛擬字段：格式化金額
transactionSchema.virtual('formattedAmount').get(function() {
  return new Intl.NumberFormat('zh-TW', {
    style: 'currency',
    currency: 'TWD'
  }).format(this.amount);
});

// 靜態方法：獲取用戶的財務統計
transactionSchema.statics.getFinancialStats = async function(userId, startDate, endDate) {
  const pipeline = [
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        createdAt: {
          $gte: startDate || new Date(0),
          $lte: endDate || new Date()
        }
      }
    },
    {
      $group: {
        _id: '$type',
        total: { $sum: '$amount' },
        count: { $sum: 1 },
        categories: {
          $push: {
            category: '$category',
            amount: '$amount'
          }
        }
      }
    }
  ];

  return await this.aggregate(pipeline);
};

// 靜態方法：獲取類別統計
transactionSchema.statics.getCategoryStats = async function(userId, type, startDate, endDate) {
  const pipeline = [
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        type: type,
        createdAt: {
          $gte: startDate || new Date(0),
          $lte: endDate || new Date()
        }
      }
    },
    {
      $group: {
        _id: '$category',
        total: { $sum: '$amount' },
        count: { $sum: 1 },
        avgAmount: { $avg: '$amount' }
      }
    },
    {
      $sort: { total: -1 }
    }
  ];

  return await this.aggregate(pipeline);
};

export default mongoose.model('Transaction', transactionSchema);

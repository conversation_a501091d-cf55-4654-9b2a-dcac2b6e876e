import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { Transaction, TransactionType, EquipmentSlot, EquipmentItem, Quest, Building, Talent, Monster, CombatState } from './types';
import { INITIAL_QUESTS, BUILDINGS, TALENT_TREE, EQUIPMENT_ITEMS, MONSTERS, MONSTER_PARTS } from './constants';
import Dashboard from './components/Dashboard';
import TransactionForm from './components/TransactionForm';
import TransactionList from './components/TransactionList';
import WiseSageModal from './components/WiseSageModal';
import SidePanel from './components/SidePanel';
import CharacterPanel from './components/CharacterPanel';
import EquipmentShop from './components/EquipmentShop';
import QuestBoard from './components/QuestBoard';
import TalentTreeView from './components/TalentTreeView';
import TownManagement from './components/TownManagement';
import ExplorationView from './components/ExplorationView';
import CombatView from './components/CombatView';
import { NotificationProvider, useNotification } from './contexts/NotificationContext';
import ToastNotification from './components/ToastNotification';

type View = 'dashboard' | 'character' | 'shop' | 'talents' | 'town' | 'exploration';

const AppContent: React.FC = () => {
    const { addToast } = useNotification();
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [level, setLevel] = useState(1);
    const [xp, setXp] = useState(0);
    const [crystals, setCrystals] = useState(10);
    const [isSageModalOpen, setIsSageModalOpen] = useState(false);
    
    const [currentView, setCurrentView] = useState<View>('dashboard');

    const [ownedEquipment, setOwnedEquipment] = useState<string[]>([]);
    const [equippedItems, setEquippedItems] = useState<Record<EquipmentSlot, string | null>>({
        [EquipmentSlot.WEAPON]: null,
        [EquipmentSlot.ARMOR]: null,
    });
    const [quests, setQuests] = useState<Quest[]>(INITIAL_QUESTS);
    
    const [builtBuildings, setBuiltBuildings] = useState<string[]>([]);
    const [uncollectedTaxes, setUncollectedTaxes] = useState(0);
    
    const [talentPoints, setTalentPoints] = useState(0);
    const [unlockedTalents, setUnlockedTalents] = useState<string[]>([]);
    
    const [inventory, setInventory] = useState<Record<string, number>>({});
    
    const [playerStats] = useState({ maxHp: 100, baseAttack: 5, baseDefense: 2 });
    const [combatState, setCombatState] = useState<CombatState>({
        isActive: false, monster: null, playerHp: 100, monsterHp: 0, combatLog: [], isPlayerTurn: true
    });

    const xpForNextLevel = useMemo(() => 50 + level * 50, [level]);

    const talentBonuses = useMemo(() => {
        const bonuses = { shopCostReduction: 0, crystalChance: 0, xpGainIncrease: 0, questXpBonus: 0, buildingCostReduction: 0, taxRateIncrease: 0 };
        unlockedTalents.forEach(talentId => {
            const talent = Object.values(TALENT_TREE).flat().find(t => t.id === talentId);
            if (!talent) return;
            switch (talent.effect.type) {
                case 'shop_cost_reduction': bonuses.shopCostReduction += talent.effect.value; break;
                case 'crystal_chance_on_transaction': bonuses.crystalChance += talent.effect.value; break;
                case 'xp_gain_increase': bonuses.xpGainIncrease += talent.effect.value; break;
                case 'quest_xp_bonus': bonuses.questXpBonus += talent.effect.value; break;
                case 'building_cost_reduction': bonuses.buildingCostReduction += talent.effect.value; break;
                case 'tax_rate_increase': bonuses.taxRateIncrease += talent.effect.value; break;
            }
        });
        return bonuses;
    }, [unlockedTalents]);
    
    const derivedPlayerStats = useMemo(() => {
        const stats = { ...playerStats };
        Object.values(equippedItems).forEach(itemId => {
            if (!itemId) return;
            const item = EQUIPMENT_ITEMS.find(i => i.id === itemId);
            if (item) {
                stats.baseAttack += item.attack || 0;
                stats.baseDefense += item.defense || 0;
            }
        });
        return {
            maxHp: stats.maxHp,
            attack: stats.baseAttack,
            defense: stats.baseDefense
        };
    }, [playerStats, equippedItems]);

    const { totalIncome, totalExpenses, expenseByCategory, incomeByCategory } = useMemo(() => {
        let income = 0, expenses = 0;
        const expenseCat: Record<string, number> = {}, incomeCat: Record<string, number> = {};
        transactions.forEach(t => {
            if (t.type === TransactionType.INCOME) {
                income += t.amount;
                incomeCat[t.category] = (incomeCat[t.category] || 0) + t.amount;
            } else {
                expenses += t.amount;
                expenseCat[t.category] = (expenseCat[t.category] || 0) + t.amount;
            }
        });
        return { totalIncome: income, totalExpenses: expenses, expenseByCategory: expenseCat, incomeByCategory: incomeCat };
    }, [transactions]);
    
    const balance = useMemo(() => totalIncome - totalExpenses, [totalIncome, totalExpenses]);

    const addXp = useCallback((amount: number) => {
        const finalAmount = Math.ceil(amount * (1 + talentBonuses.xpGainIncrease));
        if (finalAmount > 0) addToast(`+${finalAmount} XP`, 'reward');
        
        setXp(currentXp => {
            let newXp = currentXp + finalAmount;
            let newLevel = level;
            let xpToNext = 50 + newLevel * 50;
            while (newXp >= xpToNext) {
                newLevel += 1;
                newXp -= xpToNext;
                setTalentPoints(p => p + 1);
                setCrystals(c => c + (newLevel) * 5); 
                addToast(`等級提升！你現在是 ${newLevel} 級了！`, 'success');
                xpToNext = 50 + newLevel * 50;
            }
            setLevel(newLevel);
            return newXp;
        });
    }, [level, talentBonuses.xpGainIncrease, addToast]);

    const handleAddTransaction = (transaction: Omit<Transaction, 'id' | 'date'>) => {
        const newTransaction: Transaction = { ...transaction, id: new Date().toISOString() + Math.random(), date: new Date().toISOString() };
        setTransactions(prev => [newTransaction, ...prev]);
        addXp(10);
        if (talentBonuses.crystalChance > 0 && Math.random() < talentBonuses.crystalChance) {
             setCrystals(c => c + 10);
             addToast('幸運！額外獲得 10 水晶', 'reward');
        }
    };

    const handleDeleteTransaction = (id: string) => setTransactions(prev => prev.filter(t => t.id !== id));

    const handlePurchaseItem = (item: EquipmentItem) => {
        const finalCost = Math.ceil(item.cost * (1 - talentBonuses.shopCostReduction));
        if (crystals >= finalCost && !ownedEquipment.includes(item.id)) {
            setCrystals(c => c - finalCost);
            setOwnedEquipment(prev => [...prev, item.id]);
            addToast(`已購買 ${item.name}!`, 'success');
            addXp(50);
        }
    };

    const handleEquipItem = (item: EquipmentItem) => setEquippedItems(prev => ({ ...prev, [item.slot]: item.id }));
    const handleUnequipItem = (slot: EquipmentSlot) => setEquippedItems(prev => ({ ...prev, [slot]: null }));
    
    const handleBuildBuilding = (building: Building) => {
        const finalCost = Math.ceil(building.cost * (1 - talentBonuses.buildingCostReduction));
        if (crystals >= finalCost && !builtBuildings.includes(building.id) && level >= building.requiredLevel) {
            setCrystals(c => c - finalCost);
            setBuiltBuildings(prev => [...prev, building.id]);
            addToast(`已建造 ${building.name}!`, 'success');
            addXp(100);
        }
    };

    const handleCollectTaxes = () => {
        const baseAmount = Math.floor(uncollectedTaxes);
        const finalAmount = Math.ceil(baseAmount * (1 + talentBonuses.taxRateIncrease));
        if (finalAmount > 0) {
            setCrystals(c => c + finalAmount);
            setUncollectedTaxes(prev => prev - baseAmount);
        }
    };
    
    useEffect(() => {
        const market = BUILDINGS.find(b => b.id === 'market');
        if (builtBuildings.includes('market') && market?.generationRate) {
            const interval = setInterval(() => setUncollectedTaxes(prev => prev + (market.generationRate ?? 0)), 1000);
            return () => clearInterval(interval);
        }
    }, [builtBuildings]);
    
    const handleUnlockTalent = (talent: Talent) => {
        if (talentPoints >= talent.cost && level >= talent.requiredLevel && !unlockedTalents.includes(talent.id)) {
            if (talent.prerequisite && !unlockedTalents.includes(talent.prerequisite)) return;
            setTalentPoints(p => p - talent.cost);
            setUnlockedTalents(prev => [...prev, talent.id]);
        }
    };

    const handleEndCombat = useCallback((outcome: 'win' | 'lose' | 'flee') => {
        setCombatState(currentCombatState => {
            const monster = currentCombatState.monster;
            if (!monster) return currentCombatState;

            let finalLog = [...currentCombatState.combatLog];
            if (outcome === 'win') {
                finalLog.push(`你擊敗了 ${monster.name}!`);
                addToast(`你擊敗了 ${monster.name}!`, 'success');
                addXp(monster.xp);

                const newLoot: Record<string, number> = {};
                monster.drops.forEach(drop => {
                    if (Math.random() < drop.chance) {
                        const quantity = Math.floor(Math.random() * (drop.quantity[1] - drop.quantity[0] + 1)) + drop.quantity[0];
                        newLoot[drop.partId] = (newLoot[drop.partId] || 0) + quantity;
                    }
                });

                if (Object.keys(newLoot).length > 0) {
                    finalLog.push("你獲得了戰利品！");
                    setInventory(prev => {
                        const newInventory = { ...prev };
                        for (const partId in newLoot) {
                            newInventory[partId] = (newInventory[partId] || 0) + newLoot[partId];
                            const part = MONSTER_PARTS.find(p => p.id === partId);
                            if (part) {
                                addToast(`獲得戰利品: ${part.name} x${newLoot[partId]}`, 'reward');
                            }
                        }
                        return newInventory;
                    });
                }
            } else if (outcome === 'lose') {
                finalLog.push("你被擊敗了...");
            } else {
                finalLog.push("你成功逃跑了！");
            }
            
            const endState = { ...currentCombatState, combatLog: finalLog, isPlayerTurn: false };

            setTimeout(() => {
                setCombatState({ isActive: false, monster: null, playerHp: 0, monsterHp: 0, combatLog: [], isPlayerTurn: true });
            }, 2000);

            return endState;
        });
    }, [addXp, addToast]);
    
    const handleCombatAction = useCallback((action: 'attack' | 'flee') => {
        setCombatState(currentCombatState => {
            if (!currentCombatState.isActive || !currentCombatState.isPlayerTurn || !currentCombatState.monster) {
                return currentCombatState;
            }

            let nextState = { ...currentCombatState, isPlayerTurn: false };

            if (action === 'flee') {
                handleEndCombat('flee');
                return nextState;
            }

            const playerDamage = Math.max(1, derivedPlayerStats.attack - nextState.monster.defense + (Math.floor(Math.random() * 5) - 2));
            const newMonsterHp = Math.max(0, nextState.monsterHp - playerDamage);
            const playerAttackLog = [...nextState.combatLog, `你攻擊並造成了 ${playerDamage} 點傷害。`];
            
            nextState = { ...nextState, monsterHp: newMonsterHp, combatLog: playerAttackLog };

            if (newMonsterHp <= 0) {
                setTimeout(() => handleEndCombat('win'), 1000);
                return nextState;
            }

            setTimeout(() => {
                setCombatState(latestState => {
                    if (!latestState.isActive || !latestState.monster || latestState.monsterHp <= 0) {
                        return latestState;
                    }
                    const monsterDamage = Math.max(1, latestState.monster.attack - derivedPlayerStats.defense + (Math.floor(Math.random() * 3) - 1));
                    const newPlayerHp = Math.max(0, latestState.playerHp - monsterDamage);
                    const monsterAttackLog = [...latestState.combatLog, `${latestState.monster.name} 反擊，對你造成 ${monsterDamage} 點傷害。`];

                    if (newPlayerHp <= 0) {
                        setTimeout(() => handleEndCombat('lose'), 1000);
                        return { ...latestState, playerHp: newPlayerHp, combatLog: monsterAttackLog, isPlayerTurn: false };
                    } else {
                        return { ...latestState, playerHp: newPlayerHp, combatLog: monsterAttackLog, isPlayerTurn: true };
                    }
                });
            }, 1200);

            return nextState;
        });
    }, [derivedPlayerStats, handleEndCombat]);


    const handleStartCombat = (monster: Monster) => {
        const fullMonster = MONSTERS.find(m => m.id === monster.id);
        if (!fullMonster || crystals < fullMonster.huntCost || level < fullMonster.level) return;

        setCrystals(c => c - fullMonster.huntCost);
        setCombatState({
            isActive: true,
            monster: fullMonster,
            playerHp: derivedPlayerStats.maxHp,
            monsterHp: fullMonster.hp,
            combatLog: [`一隻野生的 ${fullMonster.name} 出現了！`],
            isPlayerTurn: true,
        });
    };

    useEffect(() => {
        const newlyCompletedQuests = quests.filter(q => {
            if (q.isCompleted) return false;
            switch(q.condition.type) {
                case 'earn': return transactions.filter(t => t.type === TransactionType.INCOME).length >= q.condition.amount;
                case 'spend': return transactions.filter(t => t.type === TransactionType.EXPENSE && (!q.condition.category || t.category === q.condition.category)).length >= q.condition.amount;
                case 'balance': return balance >= q.condition.amount;
                default: return false;
            }
        });
        if (newlyCompletedQuests.length > 0) {
            const newlyCompletedIds = new Set(newlyCompletedQuests.map(q => q.id));
            setQuests(prevQuests => prevQuests.map(q => newlyCompletedIds.has(q.id) ? { ...q, isCompleted: true } : q));
            newlyCompletedQuests.forEach(q => {
                addToast(`任務完成: ${q.title}`, 'success');
                const xpReward = Math.ceil(q.reward * 5 * (1 + talentBonuses.questXpBonus));
                addXp(xpReward);
                setCrystals(c => c + q.reward);
                addToast(`獲得 ${q.reward} 水晶`, 'reward');
            });
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [transactions, balance]);

    const renderView = () => {
        switch (currentView) {
            case 'character': return <CharacterPanel ownedEquipment={ownedEquipment} equippedItems={equippedItems} onEquipItem={handleEquipItem} onUnequipItem={handleUnequipItem} inventory={inventory} playerStats={derivedPlayerStats} />;
            case 'shop': return <EquipmentShop crystals={crystals} ownedEquipment={ownedEquipment} onPurchaseItem={handlePurchaseItem} talentBonuses={talentBonuses} />;
            case 'talents': return <TalentTreeView level={level} talentPoints={talentPoints} unlockedTalents={unlockedTalents} onUnlockTalent={handleUnlockTalent} />;
            case 'town': return <TownManagement crystals={crystals} level={level} builtBuildings={builtBuildings} uncollectedTaxes={uncollectedTaxes} onBuildBuilding={handleBuildBuilding} onCollectTaxes={handleCollectTaxes} talentBonuses={talentBonuses} />;
            case 'exploration': return <ExplorationView level={level} crystals={crystals} onHuntMonster={handleStartCombat} />;
            case 'dashboard':
            default:
                return (
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
                        <div className="lg:col-span-2 space-y-8">
                            <Dashboard level={level} xp={xp} xpForNextLevel={xpForNextLevel} crystals={crystals} totalIncome={totalIncome} totalExpenses={totalExpenses} balance={balance} onOpenSage={() => setIsSageModalOpen(true)} transactions={transactions} expenseByCategory={expenseByCategory} incomeByCategory={incomeByCategory} />
                            <QuestBoard quests={quests} />
                        </div>
                        <div className="space-y-8">
                            <TransactionForm onAddTransaction={handleAddTransaction} />
                            <TransactionList transactions={transactions} onDeleteTransaction={handleDeleteTransaction} />
                        </div>
                    </div>
                );
        }
    };

    return (
        <div className="bg-stone-900 text-stone-100 min-h-screen font-sans bg-cover bg-center" style={{ backgroundImage: "url('/fantasy-bg.jpg')" }}>
            <ToastNotification />
            <main className="container mx-auto p-4 md:p-8 grid grid-cols-1 md:grid-cols-4 lg:grid-cols-5 gap-8 items-start">
                <div className="md:col-span-1 lg:col-span-1">
                    <SidePanel currentView={currentView} onSetView={setCurrentView} talentPoints={talentPoints} />
                </div>
                <div className="md:col-span-3 lg:col-span-4">
                    {renderView()}
                </div>
            </main>
            <WiseSageModal isOpen={isSageModalOpen} onClose={() => setIsSageModalOpen(false)} summary={{ income: totalIncome, expenses: totalExpenses, balance, expenseByCategory }} />
            {combatState.isActive && combatState.monster && (
                <CombatView
                    playerStats={derivedPlayerStats}
                    playerHp={combatState.playerHp}
                    monster={combatState.monster}
                    monsterHp={combatState.monsterHp}
                    combatLog={combatState.combatLog}
                    isPlayerTurn={combatState.isPlayerTurn}
                    onPlayerAction={handleCombatAction}
                />
            )}
        </div>
    );
};

const App: React.FC = () => {
    return (
        <NotificationProvider>
            <AppContent />
        </NotificationProvider>
    );
};


export default App;

# RPG冒險家帳本 - 全端重構版

這是一個將原本的React單頁應用重構為Vue3 + Node.js + MongoDB全端架構的專案。

## 🏗️ 專案架構

### 技術棧
- **前端**: Vue 3 + TypeScript + Vite + Tailwind CSS + Pinia
- **後端**: Node.js + Express + MongoDB + JWT
- **AI服務**: Google Gemini API

### 專案結構
```
rpg-bookkeep-fullstack/
├── backend/                 # Node.js 後端
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── models/         # MongoDB 模型
│   │   ├── routes/         # API 路由
│   │   ├── middleware/     # 中間件
│   │   ├── services/       # 業務邏輯服務
│   │   ├── config/         # 配置文件
│   │   └── utils/          # 工具函數
│   ├── package.json
│   └── server.js
├── frontend/               # Vue3 前端
│   ├── src/
│   │   ├── components/     # Vue 組件
│   │   ├── views/          # 頁面視圖
│   │   ├── stores/         # Pinia 狀態管理
│   │   ├── services/       # API 服務
│   │   ├── types/          # TypeScript 類型
│   │   ├── utils/          # 工具函數
│   │   └── assets/         # 靜態資源
│   ├── package.json
│   └── vite.config.ts
└── README.md
```

## 🚀 快速開始

### 環境要求
- Node.js 18+
- MongoDB 6+
- npm 或 yarn

### 後端設置

1. 進入後端目錄：
```bash
cd backend
```

2. 安裝依賴：
```bash
npm install
```

3. 設置環境變數：
```bash
cp .env.example .env
```

編輯 `.env` 文件，設置以下變數：
```env
MONGODB_URI=mongodb://localhost:27017/rpg-bookkeep
JWT_SECRET=your-super-secret-jwt-key-here
GEMINI_API_KEY=your-gemini-api-key-here
PORT=5000
```

4. 啟動後端服務：
```bash
npm run dev
```

### 前端設置

1. 進入前端目錄：
```bash
cd frontend
```

2. 安裝依賴：
```bash
npm install
```

3. 啟動開發服務器：
```bash
npm run dev
```

4. 在瀏覽器中打開 `http://localhost:5173`

## 📊 資料庫設計

### 主要集合

#### Users (用戶)
- 用戶基本信息
- 認證信息
- 偏好設置

#### Characters (角色)
- 遊戲角色數據
- 等級、經驗值、水晶
- 裝備和天賦
- 庫存和建築

#### Transactions (交易)
- 財務交易記錄
- 收入和支出
- 分類和標籤

## 🎮 主要功能

### 已實現功能
- ✅ 用戶註冊和登入
- ✅ JWT 認證
- ✅ 角色系統（等級、經驗值、水晶）
- ✅ 交易管理（CRUD操作）
- ✅ 裝備系統
- ✅ 天賦樹
- ✅ 建築系統
- ✅ 戰鬥系統
- ✅ AI財務建議（Gemini）
- ✅ 響應式設計

### 待完成功能
- ⏳ 完整的前端組件實現
- ⏳ 圖表和數據可視化
- ⏳ 任務系統完整實現
- ⏳ 稅收自動生成
- ⏳ 數據匯出/匯入
- ⏳ 多語言支持

## 🔧 API 端點

### 認證
- `POST /api/auth/register` - 用戶註冊
- `POST /api/auth/login` - 用戶登入
- `GET /api/auth/verify` - 驗證令牌
- `POST /api/auth/logout` - 用戶登出

### 角色
- `GET /api/characters` - 獲取角色信息
- `PUT /api/characters` - 更新角色信息
- `POST /api/characters/equip` - 裝備物品
- `POST /api/characters/buy-equipment` - 購買裝備
- `POST /api/characters/unlock-talent` - 解鎖天賦
- `POST /api/characters/build` - 建造建築

### 交易
- `GET /api/transactions` - 獲取交易列表
- `POST /api/transactions` - 創建交易
- `PUT /api/transactions/:id` - 更新交易
- `DELETE /api/transactions/:id` - 刪除交易
- `GET /api/transactions/stats/summary` - 獲取財務統計

### 戰鬥
- `GET /api/combat/monsters` - 獲取怪物列表
- `POST /api/combat/start` - 開始戰鬥
- `POST /api/combat/resolve` - 戰鬥結算

### AI
- `POST /api/ai/financial-advice` - 獲取財務建議

## 🎨 前端架構

### 狀態管理 (Pinia)
- `authStore` - 用戶認證狀態
- `characterStore` - 角色數據
- `transactionStore` - 交易數據
- `combatStore` - 戰鬥狀態
- `toastStore` - 通知系統

### 路由
- `/login` - 登入頁面
- `/register` - 註冊頁面
- `/dashboard` - 主儀表板
- `/character` - 角色面板
- `/shop` - 裝備商店
- `/talents` - 天賦樹
- `/town` - 城鎮管理
- `/exploration` - 探索和戰鬥

## 🔒 安全性

- JWT 令牌認證
- 密碼加密 (bcrypt)
- 輸入驗證
- 速率限制
- CORS 配置
- Helmet 安全頭

## 📱 響應式設計

使用 Tailwind CSS 實現完全響應式設計，支援：
- 桌面端 (1024px+)
- 平板端 (768px-1023px)
- 手機端 (<768px)

## 🎯 與原版差異

### 改進點
1. **資料持久化**: 使用MongoDB替代記憶體存儲
2. **用戶系統**: 支援多用戶註冊和登入
3. **API架構**: RESTful API設計
4. **狀態管理**: 使用Pinia替代React useState
5. **類型安全**: 完整的TypeScript支援
6. **安全性**: JWT認證和輸入驗證
7. **可擴展性**: 模組化架構設計

### 保持的功能
- 所有原有的遊戲機制
- RPG風格的UI設計
- 財務管理核心功能
- AI智者建議功能

## 🚧 開發狀態

目前專案處於開發階段，主要的後端API和前端架構已完成，正在實現完整的前端組件。

## 📝 待辦事項

1. 完成所有Vue組件的實現
2. 實現圖表和數據可視化
3. 添加單元測試
4. 優化性能
5. 添加錯誤處理
6. 完善文檔

## 🤝 貢獻

歡迎提交Issue和Pull Request來改進這個專案！

## 📄 授權

MIT License

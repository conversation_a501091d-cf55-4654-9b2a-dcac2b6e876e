import express from 'express';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 天賦樹數據（從原始constants移植）
const TALENT_TREE = {
  "商業之路": [
    {
      id: 'bargainer_1',
      name: '議價者 I',
      icon: '💰',
      description: '商店物品價格降低 5%。',
      cost: 1,
      requiredLevel: 2,
      effect: { type: 'shop_cost_reduction', value: 0.05 },
      position: { row: 0, col: 0 }
    },
    {
      id: 'bargainer_2',
      name: '議價者 II',
      icon: '🤑',
      description: '商店物品價格再降低 10%。',
      cost: 2,
      requiredLevel: 10,
      prerequisite: 'bargainer_1',
      effect: { type: 'shop_cost_reduction', value: 0.1 },
      position: { row: 1, col: 0 }
    },
    {
      id: 'treasure_hunter_1',
      name: '寶藏獵人 I',
      icon: '💎',
      description: '每次交易有 10% 機率額外獲得 10 水晶。',
      cost: 1,
      requiredLevel: 2,
      effect: { type: 'crystal_chance_on_transaction', value: 0.1 },
      position: { row: 0, col: 1 }
    }
  ],
  "冒險之路": [
    {
      id: 'explorer_1',
      name: '探險家 I',
      icon: '🗺️',
      description: '經驗值獲取增加 15%。',
      cost: 1,
      requiredLevel: 3,
      effect: { type: 'xp_gain_increase', value: 0.15 },
      position: { row: 0, col: 0 }
    },
    {
      id: 'quest_master_1',
      name: '任務大師 I',
      icon: '📜',
      description: '任務經驗值獎勵增加 25%。',
      cost: 2,
      requiredLevel: 5,
      effect: { type: 'quest_xp_bonus', value: 0.25 },
      position: { row: 1, col: 0 }
    }
  ],
  "建設之路": [
    {
      id: 'architect_1',
      name: '建築師 I',
      icon: '🏗️',
      description: '建築成本降低 10%。',
      cost: 1,
      requiredLevel: 4,
      effect: { type: 'building_cost_reduction', value: 0.1 },
      position: { row: 0, col: 0 }
    },
    {
      id: 'tax_collector_1',
      name: '稅務官 I',
      icon: '💰',
      description: '稅收增加 20%。',
      cost: 2,
      requiredLevel: 6,
      prerequisite: 'architect_1',
      effect: { type: 'tax_rate_increase', value: 0.2 },
      position: { row: 1, col: 0 }
    }
  ]
};

// 獲取天賦樹
router.get('/', (req, res) => {
  try {
    res.json({
      talentTree: TALENT_TREE
    });
  } catch (error) {
    console.error('獲取天賦樹錯誤:', error);
    res.status(500).json({
      message: '獲取天賦樹失敗'
    });
  }
});

// 獲取單個天賦詳情
router.get('/:id', (req, res) => {
  try {
    let talent = null;
    
    // 在所有分支中搜索天賦
    for (const branch of Object.values(TALENT_TREE)) {
      talent = branch.find(t => t.id === req.params.id);
      if (talent) break;
    }
    
    if (!talent) {
      return res.status(404).json({
        message: '天賦不存在'
      });
    }

    res.json(talent);
  } catch (error) {
    console.error('獲取天賦詳情錯誤:', error);
    res.status(500).json({
      message: '獲取天賦詳情失敗'
    });
  }
});

export default router;

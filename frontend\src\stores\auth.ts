import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/types'
import { authApi } from '@/services/api'

export const useAuthStore = defineStore('auth', () => {
  // 狀態
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('auth_token'))
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 計算屬性
  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // 設置認證信息
  const setAuth = (authData: { user: User; token: string }) => {
    user.value = authData.user
    token.value = authData.token
    localStorage.setItem('auth_token', authData.token)
    error.value = null
  }

  // 清除認證信息
  const clearAuth = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('auth_token')
    error.value = null
  }

  // 登入
  const login = async (email: string, password: string) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await authApi.login({ email, password })
      setAuth(response.data)
      
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || '登入失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 註冊
  const register = async (userData: { username: string; email: string; password: string }) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await authApi.register(userData)
      setAuth(response.data)
      
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || '註冊失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (err) {
      console.error('登出請求失敗:', err)
    } finally {
      clearAuth()
    }
  }

  // 驗證令牌
  const verifyToken = async () => {
    if (!token.value) {
      throw new Error('沒有令牌')
    }

    try {
      isLoading.value = true
      const response = await authApi.verify()
      user.value = response.data.user
      return response
    } catch (err: any) {
      clearAuth()
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 更新用戶資料
  const updateProfile = async (profileData: Partial<User>) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await authApi.updateProfile(profileData)
      if (user.value) {
        user.value = { ...user.value, ...response.data.user }
      }
      
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || '更新失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 更改密碼
  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await authApi.changePassword({ currentPassword, newPassword })
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || '密碼更改失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 清除錯誤
  const clearError = () => {
    error.value = null
  }

  return {
    // 狀態
    user,
    token,
    isLoading,
    error,
    
    // 計算屬性
    isAuthenticated,
    
    // 方法
    login,
    register,
    logout,
    verifyToken,
    updateProfile,
    changePassword,
    clearAuth,
    clearError
  }
})

<template>
  <div class="card">
    <h2 class="text-2xl font-bold text-yellow-400 font-pixel mb-6">財務概覽</h2>
    
    <div v-if="transactionStore.isLoading" class="flex justify-center py-8">
      <div class="loading-spinner"></div>
    </div>
    
    <div v-else class="space-y-6">
      <!-- 財務統計卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="stat-card bg-green-600/20 border-green-500">
          <div class="text-3xl mb-2">💰</div>
          <div>
            <div class="text-sm text-green-300">總收入</div>
            <div class="text-2xl font-bold text-green-400">
              {{ formatCurrency(transactionStore.totalIncome) }}
            </div>
          </div>
        </div>
        
        <div class="stat-card bg-red-600/20 border-red-500">
          <div class="text-3xl mb-2">💸</div>
          <div>
            <div class="text-sm text-red-300">總支出</div>
            <div class="text-2xl font-bold text-red-400">
              {{ formatCurrency(transactionStore.totalExpenses) }}
            </div>
          </div>
        </div>
        
        <div class="stat-card bg-blue-600/20 border-blue-500">
          <div class="text-3xl mb-2">💎</div>
          <div>
            <div class="text-sm text-blue-300">淨餘額</div>
            <div class="text-2xl font-bold" :class="balanceColor">
              {{ formatCurrency(transactionStore.balance) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 分類支出圖表 -->
      <div v-if="expenseData.length > 0">
        <h3 class="text-lg font-bold text-stone-300 mb-4">支出分類</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
          <div 
            v-for="item in expenseData" 
            :key="item.category"
            class="bg-stone-700/50 p-3 rounded-lg text-center"
          >
            <div class="text-2xl mb-1">{{ getCategoryIcon(item.category) }}</div>
            <div class="text-xs text-stone-400 mb-1">{{ getCategoryLabel(item.category) }}</div>
            <div class="font-bold text-stone-200">{{ formatCurrency(item.amount) }}</div>
            <div class="text-xs text-stone-500">{{ item.percentage }}%</div>
          </div>
        </div>
      </div>

      <!-- 收入分類圖表 -->
      <div v-if="incomeData.length > 0">
        <h3 class="text-lg font-bold text-stone-300 mb-4">收入分類</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
          <div 
            v-for="item in incomeData" 
            :key="item.category"
            class="bg-stone-700/50 p-3 rounded-lg text-center"
          >
            <div class="text-2xl mb-1">{{ getCategoryIcon(item.category) }}</div>
            <div class="text-xs text-stone-400 mb-1">{{ getCategoryLabel(item.category) }}</div>
            <div class="font-bold text-green-400">{{ formatCurrency(item.amount) }}</div>
            <div class="text-xs text-stone-500">{{ item.percentage }}%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useTransactionStore } from '@/stores/transaction'
import { Category } from '@/types'

const transactionStore = useTransactionStore()

const balanceColor = computed(() => {
  const balance = transactionStore.balance
  if (balance > 0) return 'text-green-400'
  if (balance < 0) return 'text-red-400'
  return 'text-stone-400'
})

const expenseData = computed(() => {
  const expenses = transactionStore.expenseByCategory
  const total = transactionStore.totalExpenses
  
  return Object.entries(expenses)
    .map(([category, amount]) => ({
      category: category as Category,
      amount,
      percentage: total > 0 ? Math.round((amount / total) * 100) : 0
    }))
    .sort((a, b) => b.amount - a.amount)
})

const incomeData = computed(() => {
  const income = transactionStore.incomeByCategory
  const total = transactionStore.totalIncome
  
  return Object.entries(income)
    .map(([category, amount]) => ({
      category: category as Category,
      amount,
      percentage: total > 0 ? Math.round((amount / total) * 100) : 0
    }))
    .sort((a, b) => b.amount - a.amount)
})

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-TW', {
    style: 'currency',
    currency: 'TWD'
  }).format(amount)
}

const getCategoryIcon = (category: Category) => {
  const icons: Record<Category, string> = {
    [Category.SALARY]: '💼',
    [Category.INVESTMENT]: '📈',
    [Category.BUSINESS]: '🏪',
    [Category.OTHER_INCOME]: '💰',
    [Category.FOOD]: '🍽️',
    [Category.TRANSPORT]: '🚗',
    [Category.ENTERTAINMENT]: '🎮',
    [Category.SHOPPING]: '🛒',
    [Category.BILLS]: '📄',
    [Category.HEALTHCARE]: '🏥',
    [Category.EDUCATION]: '📚',
    [Category.OTHER_EXPENSE]: '💸'
  }
  return icons[category] || '❓'
}

const getCategoryLabel = (category: Category) => {
  const labels: Record<Category, string> = {
    [Category.SALARY]: '薪資',
    [Category.INVESTMENT]: '投資',
    [Category.BUSINESS]: '營業',
    [Category.OTHER_INCOME]: '其他收入',
    [Category.FOOD]: '餐飲',
    [Category.TRANSPORT]: '交通',
    [Category.ENTERTAINMENT]: '娛樂',
    [Category.SHOPPING]: '購物',
    [Category.BILLS]: '帳單',
    [Category.HEALTHCARE]: '醫療',
    [Category.EDUCATION]: '教育',
    [Category.OTHER_EXPENSE]: '其他支出'
  }
  return labels[category] || '未知'
}
</script>

<style scoped>
.loading-spinner {
  @apply border-4 border-stone-600 border-t-yellow-400 rounded-full w-8 h-8 animate-spin;
}

.stat-card {
  @apply p-4 rounded-lg border-2 flex items-center space-x-4;
}
</style>

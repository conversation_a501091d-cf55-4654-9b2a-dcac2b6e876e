import express from 'express';
import { body, validationResult } from 'express-validator';
import User from '../models/User.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要認證
router.use(authenticateToken);

// 獲取用戶資料
router.get('/profile', async (req, res) => {
  try {
    const user = await User.findById(req.user._id).populate('character');
    
    if (!user) {
      return res.status(404).json({
        message: '用戶不存在'
      });
    }

    res.json(user);

  } catch (error) {
    console.error('獲取用戶資料錯誤:', error);
    res.status(500).json({
      message: '獲取用戶資料失敗'
    });
  }
});

// 更新用戶資料
router.put('/profile', [
  body('username')
    .optional()
    .isLength({ min: 3, max: 30 })
    .withMessage('用戶名必須在3-30個字符之間')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用戶名只能包含字母、數字和下劃線'),
  body('avatar')
    .optional()
    .isString()
    .withMessage('頭像必須是字符串'),
  body('preferences.theme')
    .optional()
    .isIn(['dark', 'light'])
    .withMessage('主題必須是dark或light'),
  body('preferences.language')
    .optional()
    .isIn(['zh-TW', 'zh-CN', 'en'])
    .withMessage('語言必須是zh-TW、zh-CN或en'),
  body('preferences.notifications')
    .optional()
    .isBoolean()
    .withMessage('通知設置必須是布爾值')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: '輸入驗證失敗',
        errors: errors.array()
      });
    }

    const { username, avatar, preferences } = req.body;
    const updateData = {};

    if (username) {
      // 檢查用戶名是否已被使用
      const existingUser = await User.findOne({ 
        username, 
        _id: { $ne: req.user._id } 
      });
      
      if (existingUser) {
        return res.status(400).json({
          message: '用戶名已被使用'
        });
      }
      
      updateData.username = username;
    }

    if (avatar) updateData.avatar = avatar;
    if (preferences) updateData.preferences = { ...req.user.preferences, ...preferences };

    const user = await User.findByIdAndUpdate(
      req.user._id,
      updateData,
      { new: true, runValidators: true }
    );

    res.json({
      message: '用戶資料更新成功',
      user
    });

  } catch (error) {
    console.error('更新用戶資料錯誤:', error);
    res.status(500).json({
      message: '更新用戶資料失敗'
    });
  }
});

// 更改密碼
router.put('/password', [
  body('currentPassword')
    .notEmpty()
    .withMessage('當前密碼是必需的'),
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('新密碼至少需要6個字符')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: '輸入驗證失敗',
        errors: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;

    // 獲取用戶（包含密碼）
    const user = await User.findById(req.user._id).select('+password');
    
    if (!user) {
      return res.status(404).json({
        message: '用戶不存在'
      });
    }

    // 驗證當前密碼
    const isCurrentPasswordValid = await user.comparePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        message: '當前密碼錯誤'
      });
    }

    // 更新密碼
    user.password = newPassword;
    await user.save();

    res.json({
      message: '密碼更新成功'
    });

  } catch (error) {
    console.error('更改密碼錯誤:', error);
    res.status(500).json({
      message: '更改密碼失敗'
    });
  }
});

// 刪除帳戶
router.delete('/account', [
  body('password')
    .notEmpty()
    .withMessage('密碼是必需的'),
  body('confirmation')
    .equals('DELETE')
    .withMessage('請輸入DELETE確認刪除')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: '輸入驗證失敗',
        errors: errors.array()
      });
    }

    const { password } = req.body;

    // 獲取用戶（包含密碼）
    const user = await User.findById(req.user._id).select('+password');
    
    if (!user) {
      return res.status(404).json({
        message: '用戶不存在'
      });
    }

    // 驗證密碼
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(400).json({
        message: '密碼錯誤'
      });
    }

    // 軟刪除（設為非活躍）而不是真正刪除
    user.isActive = false;
    await user.save();

    res.json({
      message: '帳戶已停用'
    });

  } catch (error) {
    console.error('刪除帳戶錯誤:', error);
    res.status(500).json({
      message: '刪除帳戶失敗'
    });
  }
});

export default router;

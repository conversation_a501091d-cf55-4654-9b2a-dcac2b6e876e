import axios from 'axios'
import type { 
  User, 
  Character, 
  Transaction, 
  EquipmentItem, 
  Quest, 
  Talent, 
  Building, 
  Monster,
  MonsterPart,
  FinancialStats,
  PaginatedResponse,
  ApiResponse 
} from '@/types'

// 創建 axios 實例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 請求攔截器 - 添加認證令牌
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 響應攔截器 - 處理錯誤
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // 令牌過期或無效，清除本地存儲並重定向到登入頁面
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 認證 API
export const authApi = {
  login: (credentials: { email: string; password: string }) =>
    api.post<ApiResponse<{ user: User; token: string }>>('/auth/login', credentials),
  
  register: (userData: { username: string; email: string; password: string }) =>
    api.post<ApiResponse<{ user: User; token: string }>>('/auth/register', userData),
  
  logout: () =>
    api.post<ApiResponse>('/auth/logout'),
  
  verify: () =>
    api.get<ApiResponse<{ user: User; character: Character | null }>>('/auth/verify'),
  
  updateProfile: (profileData: Partial<User>) =>
    api.put<ApiResponse<{ user: User }>>('/users/profile', profileData),
  
  changePassword: (passwordData: { currentPassword: string; newPassword: string }) =>
    api.put<ApiResponse>('/users/password', passwordData),
}

// 角色 API
export const characterApi = {
  getCharacter: () =>
    api.get<Character>('/characters'),
  
  updateCharacter: (updates: Partial<Character>) =>
    api.put<ApiResponse<{ character: Character }>>('/characters', updates),
  
  equipItem: (itemId: string, slot: 'weapon' | 'armor') =>
    api.post<ApiResponse<{ character: Character }>>('/characters/equip', { itemId, slot }),
  
  unequipItem: (slot: 'weapon' | 'armor') =>
    api.post<ApiResponse<{ character: Character }>>('/characters/unequip', { slot }),
  
  buyEquipment: (itemId: string, cost: number) =>
    api.post<ApiResponse<{ character: Character; rewards: any }>>('/characters/buy-equipment', { itemId, cost }),
  
  unlockTalent: (talentId: string, cost: number, requiredLevel: number, prerequisite?: string) =>
    api.post<ApiResponse<{ character: Character }>>('/characters/unlock-talent', { 
      talentId, cost, requiredLevel, prerequisite 
    }),
  
  buildBuilding: (buildingId: string, cost: number, requiredLevel: number) =>
    api.post<ApiResponse<{ character: Character; rewards: any }>>('/characters/build', { 
      buildingId, cost, requiredLevel 
    }),
  
  collectTaxes: () =>
    api.post<ApiResponse<{ character: Character; collectedAmount: number }>>('/characters/collect-taxes'),
}

// 交易 API
export const transactionApi = {
  getTransactions: (params?: {
    page?: number
    limit?: number
    type?: string
    category?: string
    startDate?: string
    endDate?: string
    search?: string
  }) =>
    api.get<PaginatedResponse<Transaction>>('/transactions', { params }),
  
  createTransaction: (transactionData: Omit<Transaction, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) =>
    api.post<ApiResponse<{ transaction: Transaction; rewards?: any }>>('/transactions', transactionData),
  
  getTransaction: (id: string) =>
    api.get<Transaction>(`/transactions/${id}`),
  
  updateTransaction: (id: string, updates: Partial<Transaction>) =>
    api.put<ApiResponse<{ transaction: Transaction }>>(`/transactions/${id}`, updates),
  
  deleteTransaction: (id: string) =>
    api.delete<ApiResponse>(`/transactions/${id}`),
  
  getStats: (params?: { startDate?: string; endDate?: string }) =>
    api.get<{
      summary: any[]
      incomeByCategory: any[]
      expenseByCategory: any[]
    }>('/transactions/stats/summary', { params }),
}

// 裝備 API
export const equipmentApi = {
  getEquipment: () =>
    api.get<{ equipment: EquipmentItem[] }>('/equipment'),
  
  getEquipmentById: (id: string) =>
    api.get<EquipmentItem>(`/equipment/${id}`),
}

// 任務 API
export const questApi = {
  getQuests: () =>
    api.get<{ quests: Quest[] }>('/quests'),
  
  getQuestById: (id: string) =>
    api.get<Quest>(`/quests/${id}`),
}

// 天賦 API
export const talentApi = {
  getTalents: () =>
    api.get<{ talentTree: Record<string, Talent[]> }>('/talents'),
  
  getTalentById: (id: string) =>
    api.get<Talent>(`/talents/${id}`),
}

// 建築 API
export const buildingApi = {
  getBuildings: () =>
    api.get<{ buildings: Building[] }>('/buildings'),
  
  getBuildingById: (id: string) =>
    api.get<Building>(`/buildings/${id}`),
}

// 戰鬥 API
export const combatApi = {
  getMonsters: () =>
    api.get<{ monsters: Monster[] }>('/combat/monsters'),
  
  getMonsterParts: () =>
    api.get<{ parts: MonsterPart[] }>('/combat/parts'),
  
  startCombat: (monsterId: string) =>
    api.post<ApiResponse<{ 
      combat: {
        monster: Monster
        playerHp: number
        monsterHp: number
        playerStats: any
      }
      character: Character
    }>>('/combat/start', { monsterId }),
  
  resolveCombat: (monsterId: string, outcome: 'win' | 'lose' | 'flee') =>
    api.post<ApiResponse<{
      outcome: string
      rewards: any
      character: Character
    }>>('/combat/resolve', { monsterId, outcome }),
}

// AI API
export const aiApi = {
  getFinancialAdvice: () =>
    api.post<ApiResponse<{
      advice: string
      summary: FinancialStats
    }>>('/ai/financial-advice'),
}

export default api

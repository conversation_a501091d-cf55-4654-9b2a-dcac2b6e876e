<template>
  <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 標題 -->
      <div class="text-center">
        <h1 class="text-4xl font-bold text-yellow-500 font-pixel mb-2">
          財富冒險
        </h1>
        <h2 class="text-2xl font-bold text-stone-100 mb-2">
          歡迎回來，冒險家！
        </h2>
        <p class="text-stone-400">
          登入你的帳戶繼續你的冒險之旅
        </p>
      </div>

      <!-- 登入表單 -->
      <form @submit.prevent="handleLogin" class="mt-8 space-y-6">
        <div class="card">
          <div class="space-y-4">
            <!-- 電子郵件 -->
            <div>
              <label for="email" class="block text-sm font-medium text-stone-300 mb-2">
                電子郵件
              </label>
              <input
                id="email"
                v-model="form.email"
                type="email"
                required
                class="input-field"
                placeholder="請輸入你的電子郵件"
                :disabled="authStore.isLoading"
              />
            </div>

            <!-- 密碼 -->
            <div>
              <label for="password" class="block text-sm font-medium text-stone-300 mb-2">
                密碼
              </label>
              <input
                id="password"
                v-model="form.password"
                type="password"
                required
                class="input-field"
                placeholder="請輸入你的密碼"
                :disabled="authStore.isLoading"
              />
            </div>

            <!-- 錯誤訊息 -->
            <div v-if="authStore.error" class="text-red-400 text-sm text-center">
              {{ authStore.error }}
            </div>

            <!-- 登入按鈕 -->
            <button
              type="submit"
              :disabled="authStore.isLoading"
              class="w-full btn-primary flex items-center justify-center space-x-2"
            >
              <div v-if="authStore.isLoading" class="loading-spinner"></div>
              <span>{{ authStore.isLoading ? '登入中...' : '登入' }}</span>
            </button>
          </div>
        </div>

        <!-- 註冊連結 -->
        <div class="text-center">
          <p class="text-stone-400">
            還沒有帳戶？
            <router-link 
              to="/register" 
              class="text-yellow-400 hover:text-yellow-300 font-medium transition-colors"
            >
              立即註冊
            </router-link>
          </p>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToastStore } from '@/stores/toast'

const router = useRouter()
const authStore = useAuthStore()
const toastStore = useToastStore()

const form = reactive({
  email: '',
  password: ''
})

const handleLogin = async () => {
  try {
    authStore.clearError()
    await authStore.login(form.email, form.password)
    
    toastStore.success('登入成功！歡迎回來！')
    router.push('/dashboard')
  } catch (error: any) {
    toastStore.error(authStore.error || '登入失敗，請稍後再試')
  }
}

onMounted(() => {
  // 清除之前的錯誤
  authStore.clearError()
})
</script>

<style scoped>
.loading-spinner {
  @apply border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin;
}
</style>

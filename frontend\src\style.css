@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局樣式 */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-stone-900 text-stone-100 font-medieval;
  }
}

/* 組件樣式 */
@layer components {
  .btn-primary {
    @apply bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-stone-600 hover:bg-stone-700 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-stone-800/80 p-6 rounded-lg border-2 border-stone-700 shadow-lg;
  }
  
  .input-field {
    @apply w-full px-3 py-2 bg-stone-700 border border-stone-600 rounded-lg text-stone-100 placeholder-stone-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent;
  }
  
  .select-field {
    @apply w-full px-3 py-2 bg-stone-700 border border-stone-600 rounded-lg text-stone-100 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent;
  }
  
  .stat-box {
    @apply bg-stone-900/50 p-4 rounded-lg flex items-center space-x-4 border border-stone-700;
  }
  
  .nav-button {
    @apply w-full flex items-center justify-between space-x-3 px-4 py-3 rounded-lg transition-colors text-left;
  }
  
  .nav-button.active {
    @apply bg-yellow-600/20 text-yellow-300 border-l-4 border-yellow-400;
  }
  
  .nav-button:not(.active) {
    @apply text-stone-400 hover:bg-stone-700/50 hover:text-white;
  }
  
  .toast {
    @apply fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm;
  }
  
  .toast.success {
    @apply bg-green-600 text-white;
  }
  
  .toast.error {
    @apply bg-red-600 text-white;
  }
  
  .toast.warning {
    @apply bg-yellow-600 text-white;
  }
  
  .toast.info {
    @apply bg-blue-600 text-white;
  }
  
  .toast.reward {
    @apply bg-purple-600 text-white;
  }
}

/* 工具樣式 */
@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }
  
  .glow {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
  }
  
  .glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }
  
  .glow-red {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }
  
  .glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  
  .glow-purple {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
  }
}

/* 動畫 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.bounce-animation {
  animation: bounce 1s ease-in-out;
}


// Fix: Create full content for types.ts to define all data structures.
export enum TransactionType {
    INCOME = 'income',
    EXPENSE = 'expense',
}

export type Category = 
    '薪水' | '任務獎勵' | '稅金' | // income
    '補給品' | '裝備' | '建設' | '餐飲' | '交通' | '娛樂' | '狩獵' | '其他'; // expense

export interface Transaction {
    id: string;
    description: string;
    amount: number;
    type: TransactionType;
    category: Category;
    date: string;
}

export enum EquipmentSlot {
    WEAPON = 'weapon',
    ARMOR = 'armor',
}

export interface EquipmentItem {
    id: string;
    name: string;
    icon: string;
    cost: number;
    slot: EquipmentSlot;
    attack?: number;
    defense?: number;
    bonus?: {
        type: string;
        value: number;
        description: string;
    };
}

export interface Quest {
    id: string;
    title: string;
    description: string;
    isCompleted: boolean;
    reward: number; // crystals
    condition: {
        type: 'earn' | 'spend' | 'balance';
        amount: number;
        category?: Category;
    };
}

export interface Building {
    id: string;
    name: string;
    description: string;
    cost: number; // crystals
    requiredLevel: number;
    generationRate?: number; // crystals per second
}

export enum TalentEffectType {
    SHOP_COST_REDUCTION = 'shop_cost_reduction',
    CRYSTAL_CHANCE_ON_TRANSACTION = 'crystal_chance_on_transaction',
    XP_GAIN_INCREASE = 'xp_gain_increase',
    QUEST_XP_BONUS = 'quest_xp_bonus',
    BUILDING_COST_REDUCTION = 'building_cost_reduction',
    TAX_RATE_INCREASE = 'tax_rate_increase',
}

export interface Talent {
    id: string;
    name: string;
    description: string;
    icon: string;
    cost: number; // talent points
    requiredLevel: number;
    prerequisite?: string;
    effect: {
        type: TalentEffectType;
        value: number; // e.g., 0.05 for 5%
    };
    position: {
        row: number;
        col: number;
    };
}

export interface MonsterPart {
    id: string;
    name: string;
    icon: string;
}

export interface Monster {
    id: string;
    name: string;
    icon: string;
    level: number;
    hp: number;
    attack: number;
    defense: number;
    huntCost: number; // crystals
    xp: number;
    drops: {
        partId: string;
        chance: number; // 0 to 1
        quantity: [number, number]; // min, max
    }[];
}

export interface Location {
    id: string;
    name: string;
    requiredLevel: number;
    monsters: string[]; // array of monster ids
}

export interface CombatState {
    isActive: boolean;
    monster: Monster | null;
    playerHp: number;
    monsterHp: number;
    combatLog: string[];
    isPlayerTurn: boolean;
}

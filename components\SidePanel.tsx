import React from 'react';

type View = 'dashboard' | 'character' | 'shop' | 'talents' | 'town' | 'exploration';

interface SidePanelProps {
    currentView: View;
    onSetView: (view: View) => void;
    talentPoints: number;
}

const NavButton: React.FC<{ icon: string; label: string; isActive: boolean; onClick: () => void; notificationCount?: number }> = ({ icon, label, isActive, onClick, notificationCount }) => (
    <button
        onClick={onClick}
        className={`w-full flex items-center justify-between space-x-3 px-4 py-3 rounded-lg transition-colors text-left ${
            isActive ? 'bg-yellow-600/20 text-yellow-300 border-l-4 border-yellow-400' : 'text-gray-400 hover:bg-stone-700/50 hover:text-white'
        }`}
    >
        <div className="flex items-center space-x-3">
            <span className="text-2xl">{icon}</span>
            <span className="font-bold">{label}</span>
        </div>
        {notificationCount && notificationCount > 0 && (
            <div className="bg-purple-600 text-white text-xs font-bold rounded-full h-6 w-6 flex items-center justify-center font-pixel">
                {notificationCount}
            </div>
        )}
    </button>
);


const SidePanel: React.FC<SidePanelProps> = ({ currentView, onSetView, talentPoints }) => {
    return (
        <div className="bg-stone-900/80 p-4 rounded-lg flex flex-col space-y-2 h-full border-r-2 border-stone-800">
            <h1 className="text-3xl font-bold text-center text-yellow-500 mb-6 font-pixel">財富冒險</h1>
            <NavButton icon="📊" label="儀表板" isActive={currentView === 'dashboard'} onClick={() => onSetView('dashboard')} />
            <NavButton icon="🧑‍" label="角色" isActive={currentView === 'character'} onClick={() => onSetView('character')} />
            <NavButton icon="🏪" label="商店" isActive={currentView === 'shop'} onClick={() => onSetView('shop')} />
            <NavButton icon="✨" label="天賦" isActive={currentView === 'talents'} onClick={() => onSetView('talents')} notificationCount={talentPoints} />
            <NavButton icon="🏰" label="城鎮" isActive={currentView === 'town'} onClick={() => onSetView('town')} />
            <NavButton icon="🗺️" label="探索" isActive={currentView === 'exploration'} onClick={() => onSetView('exploration')} />
        </div>
    );
};

export default SidePanel;
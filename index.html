<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RPG冒險家帳本</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=MedievalSharp&family=Press+Start+2P&display=swap" rel="stylesheet">
    <style>
      body {
        font-family: 'MedievalSharp', cursive;
      }
      .font-pixel {
        font-family: 'Press Start 2P', cursive;
      }
      @keyframes shake {
        10%, 90% { transform: translate3d(-1px, 0, 0); }
        20%, 80% { transform: translate3d(2px, 0, 0); }
        30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
        40%, 60% { transform: translate3d(4px, 0, 0); }
      }
      .animate-shake {
        animation: shake 0.4s cubic-bezier(.36,.07,.19,.97) both;
      }
      .toast-item {
        animation: slideInRight 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both,
                   fadeOut 0.5s cubic-bezier(0.550, 0.085, 0.680, 0.530) 3.5s both;
      }

      @keyframes slideInRight {
        0% {
          transform: translateX(100px);
          opacity: 0;
        }
        100% {
          transform: translateX(0);
          opacity: 1;
        }
      }

      @keyframes fadeOut {
        0% {
          opacity: 1;
        }
        100% {
          opacity: 0;
        }
      }
    </style>
<script type="importmap">
{
  "imports": {
    "react": "https://aistudiocdn.com/react@^19.1.1",
    "react-dom/": "https://aistudiocdn.com/react-dom@^19.1.1/",
    "react/": "https://aistudiocdn.com/react@^19.1.1/",
    "@google/genai": "https://aistudiocdn.com/@google/genai@^1.19.0",
    "recharts": "https://aistudiocdn.com/recharts@^3.2.0",
    "uuid": "https://aistudiocdn.com/uuid@^13.0.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-gray-900 text-white">
<div id="root"></div>
<script type="module" src="/index.tsx"></script>
</body>
</html>
<template>
  <div class="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
    <div class="bg-stone-800 border-2 border-stone-600 rounded-lg max-w-md w-full p-6">
      <h2 class="text-2xl font-bold text-yellow-400 font-pixel mb-4 text-center">
        戰鬥中
      </h2>
      
      <div v-if="combatStore.currentMonster" class="space-y-6">
        <!-- 怪物信息 -->
        <div class="text-center">
          <div class="text-6xl mb-2">{{ combatStore.currentMonster.emoji }}</div>
          <h3 class="text-xl font-bold text-stone-200">{{ combatStore.currentMonster.name }}</h3>
          <div class="text-sm text-stone-400">等級 {{ combatStore.currentMonster.level }}</div>
        </div>

        <!-- 血量條 -->
        <div class="space-y-3">
          <!-- 玩家血量 -->
          <div>
            <div class="flex justify-between text-sm text-stone-300 mb-1">
              <span>你的生命值</span>
              <span>{{ combatStore.playerHp }} / 100</span>
            </div>
            <div class="w-full bg-stone-700 rounded-full h-3">
              <div 
                class="bg-red-500 h-3 rounded-full transition-all duration-500"
                :style="{ width: `${combatStore.playerHp}%` }"
              ></div>
            </div>
          </div>

          <!-- 怪物血量 -->
          <div>
            <div class="flex justify-between text-sm text-stone-300 mb-1">
              <span>{{ combatStore.currentMonster.name }}的生命值</span>
              <span>{{ combatStore.monsterHp }} / {{ combatStore.currentMonster.hp }}</span>
            </div>
            <div class="w-full bg-stone-700 rounded-full h-3">
              <div 
                class="bg-green-500 h-3 rounded-full transition-all duration-500"
                :style="{ width: `${(combatStore.monsterHp / combatStore.currentMonster.hp) * 100}%` }"
              ></div>
            </div>
          </div>
        </div>

        <!-- 戰鬥日誌 -->
        <div class="bg-stone-900 p-3 rounded-lg max-h-32 overflow-y-auto">
          <div 
            v-for="(log, index) in combatStore.combatLog" 
            :key="index"
            class="text-sm text-stone-300 mb-1"
          >
            {{ log }}
          </div>
        </div>

        <!-- 行動按鈕 -->
        <div v-if="combatStore.isPlayerTurn" class="grid grid-cols-2 gap-3">
          <button
            @click="handleAttack"
            class="btn-primary"
          >
            ⚔️ 攻擊
          </button>
          <button
            @click="handleFlee"
            class="btn-secondary"
          >
            🏃 逃跑
          </button>
        </div>

        <div v-else class="text-center text-stone-400">
          等待怪物行動...
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCombatStore } from '@/stores/combat'
import { useCharacterStore } from '@/stores/character'

const combatStore = useCombatStore()
const characterStore = useCharacterStore()

const handleAttack = () => {
  const playerStats = {
    attack: characterStore.totalAttack,
    defense: characterStore.totalDefense
  }
  combatStore.playerAction('attack', playerStats)
}

const handleFlee = () => {
  combatStore.playerAction('flee', {})
}
</script>

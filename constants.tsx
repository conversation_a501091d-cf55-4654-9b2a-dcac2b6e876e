
import { Quest, Category, Building, EquipmentItem, EquipmentSlot, Talent, TalentEffectType, MonsterPart, Monster, Location } from './types';

export const CATEGORIES: Category[] = [
    '補給品', '裝備', '餐飲', '交通', '娛樂', '建設', '狩獵', '薪水', '任務獎勵', '稅金', '其他'
];

export const CATEGORY_ICONS: Record<Category, string> = {
    '薪水': '💼',
    '任務獎勵': '📜',
    '稅金': '📈',
    '補給品': '🧪',
    '裝備': '⚔️',
    '建設': '🔨',
    '狩獵': '🏹',
    '餐飲': '🍖',
    '交通': '🐎',
    '娛樂': '🎲',
    '其他': '📦',
};

export const INITIAL_QUESTS: Quest[] = [
    {
        id: 'earn_first_gold',
        title: '第一次收入',
        description: '記錄你的第一筆收入。',
        isCompleted: false,
        reward: 10,
        condition: { type: 'earn', amount: 1 }
    },
    {
        id: 'first_expense',
        title: '第一次花費',
        description: '記錄你的第一筆支出。',
        isCompleted: false,
        reward: 5,
        condition: { type: 'spend', amount: 1 }
    },
    {
        id: 'supply_run',
        title: '補給採買',
        description: '進行 3 次「補給品」類別的消費。',
        isCompleted: false,
        reward: 20,
        condition: { type: 'spend', amount: 3, category: '補給品' }
    },
     {
        id: 'balance_1000',
        title: '小有積蓄',
        description: '讓你的金幣餘額達到 1000。',
        isCompleted: false,
        reward: 50,
        condition: { type: 'balance', amount: 1000 }
    },
];

export const EQUIPMENT_ITEMS: EquipmentItem[] = [
    {
        id: 'sword_of_avarice',
        name: '貪婪之劍',
        icon: '🗡️',
        cost: 100,
        slot: EquipmentSlot.WEAPON,
        attack: 8,
        bonus: {
            type: 'xp_gain',
            value: 0.1,
            description: '+10% 經驗值獲取'
        }
    },
    {
        id: 'merchants_garb',
        name: '商賈之袍',
        icon: '🧥',
        cost: 150,
        slot: EquipmentSlot.ARMOR,
        defense: 5,
        bonus: {
            type: 'tax_rate',
            value: 0.15,
            description: '+15% 稅收'
        }
    }
];

export const BUILDINGS: Building[] = [
    {
        id: 'market',
        name: '市集',
        description: '建造一個市集以開始徵收稅金。',
        cost: 200,
        requiredLevel: 5,
        generationRate: 0.5, // 0.5 crystal per second
    },
];

export const TALENT_TREE: Record<string, Talent[]> = {
    "商業之路": [
        {
            id: 'bargainer_1',
            name: '議價者 I',
            icon: '💰',
            description: '商店物品價格降低 5%。',
            cost: 1,
            requiredLevel: 2,
            effect: { type: TalentEffectType.SHOP_COST_REDUCTION, value: 0.05 },
            position: { row: 0, col: 0 }
        },
        {
            id: 'bargainer_2',
            name: '議價者 II',
            icon: '🤑',
            description: '商店物品價格再降低 10%。',
            cost: 2,
            requiredLevel: 10,
            prerequisite: 'bargainer_1',
            effect: { type: TalentEffectType.SHOP_COST_REDUCTION, value: 0.1 },
            position: { row: 1, col: 0 }
        },
        {
            id: 'treasure_hunter_1',
            name: '寶藏獵人 I',
            icon: '💎',
            description: '每次交易有 10% 機率額外獲得 10 水晶。',
            cost: 1,
            requiredLevel: 2,
            effect: { type: TalentEffectType.CRYSTAL_CHANCE_ON_TRANSACTION, value: 0.1 },
            position: { row: 0, col: 1 }
        },
        {
            id: 'treasure_hunter_2',
            name: '寶藏獵人 II',
            icon: '✨',
            description: '觸發額外水晶的機率提高 10%。',
            cost: 2,
            requiredLevel: 10,
            prerequisite: 'treasure_hunter_1',
            effect: { type: TalentEffectType.CRYSTAL_CHANCE_ON_TRANSACTION, value: 0.1 },
            position: { row: 1, col: 1 }
        },
    ],
    "學者之路": [
         {
            id: 'fast_learner_1',
            name: '快速學習者 I',
            icon: '🧠',
            description: '所有來源的經驗值獲取提高 10%。',
            cost: 2,
            requiredLevel: 5,
            effect: { type: TalentEffectType.XP_GAIN_INCREASE, value: 0.1 },
            position: { row: 0, col: 0 }
        },
        {
            id: 'fast_learner_2',
            name: '快速學習者 II',
            icon: '🚀',
            description: '所有來源的經驗值獲取再提高 15%。',
            cost: 3,
            requiredLevel: 12,
            prerequisite: 'fast_learner_1',
            effect: { type: TalentEffectType.XP_GAIN_INCREASE, value: 0.15 },
            position: { row: 1, col: 0 }
        },
        {
            id: 'quest_master_1',
            name: '任務大師 I',
            icon: '📜',
            description: '任務經驗值獎勵提高 15%。',
            cost: 2,
            requiredLevel: 5,
            effect: { type: TalentEffectType.QUEST_XP_BONUS, value: 0.15 },
            position: { row: 0, col: 1 }
        },
        {
            id: 'quest_master_2',
            name: '任務大師 II',
            icon: '👑',
            description: '任務經驗值獎勵再提高 20%。',
            cost: 3,
            requiredLevel: 12,
            prerequisite: 'quest_master_1',
            effect: { type: TalentEffectType.QUEST_XP_BONUS, value: 0.20 },
            position: { row: 1, col: 1 }
        },
    ],
    "統御之路": [
        {
            id: 'architect_1',
            name: '建築師 I',
            icon: '🔨',
            description: '建築成本降低 10%。',
            cost: 3,
            requiredLevel: 8,
            effect: { type: TalentEffectType.BUILDING_COST_REDUCTION, value: 0.1 },
            position: { row: 0, col: 0 }
        },
        {
            id: 'architect_2',
            name: '建築師 II',
            icon: '🏛️',
            description: '建築成本再降低 15%。',
            cost: 4,
            requiredLevel: 15,
            prerequisite: 'architect_1',
            effect: { type: TalentEffectType.BUILDING_COST_REDUCTION, value: 0.15 },
            position: { row: 1, col: 0 }
        },
        {
            id: 'tax_collector_1',
            name: '稅務官 I',
            icon: '📈',
            description: '稅收收入提高 20%。',
            cost: 3,
            requiredLevel: 8,
            effect: { type: TalentEffectType.TAX_RATE_INCREASE, value: 0.2 },
            position: { row: 0, col: 1 }
        },
        {
            id: 'tax_collector_2',
            name: '稅務官 II',
            icon: '🏦',
            description: '稅收收入再提高 25%。',
            cost: 4,
            requiredLevel: 15,
            prerequisite: 'tax_collector_1',
            effect: { type: TalentEffectType.TAX_RATE_INCREASE, value: 0.25 },
            position: { row: 1, col: 1 }
        },
    ]
};

// --- Exploration & Monster Constants ---

export const MONSTER_PARTS: MonsterPart[] = [
    { id: 'rat_tail', name: '巨大的老鼠尾巴', icon: '🐀' },
    { id: 'wolf_pelt', name: '狼的毛皮', icon: '🐺' },
    { id: 'bone_fragment', name: '骷髏碎片', icon: '💀' },
];

export const MONSTERS: Monster[] = [
    {
        id: 'giant_rat',
        name: '巨大老鼠',
        icon: '🐀',
        level: 1,
        hp: 30,
        attack: 6,
        defense: 2,
        huntCost: 5,
        xp: 10,
        drops: [
            { partId: 'rat_tail', chance: 0.8, quantity: [1, 2] },
        ]
    },
    {
        id: 'timber_wolf',
        name: '森林狼',
        icon: '🐺',
        level: 5,
        hp: 60,
        attack: 12,
        defense: 5,
        huntCost: 20,
        xp: 30,
        drops: [
            { partId: 'wolf_pelt', chance: 0.6, quantity: [1, 1] },
        ]
    },
    {
        id: 'risen_skeleton',
        name: '復活的骷髏',
        icon: '💀',
        level: 8,
        hp: 80,
        attack: 18,
        defense: 8,
        huntCost: 35,
        xp: 50,
        drops: [
            { partId: 'bone_fragment', chance: 0.9, quantity: [1, 3] },
        ]
    }
];

export const LOCATIONS: Location[] = [
    {
        id: 'whispering_woods',
        name: '低語森林',
        requiredLevel: 1,
        monsters: ['giant_rat', 'timber_wolf'],
    },
    {
        id: 'sunken_crypt',
        name: '沉沒墓穴',
        requiredLevel: 7,
        monsters: ['risen_skeleton'],
    }
];

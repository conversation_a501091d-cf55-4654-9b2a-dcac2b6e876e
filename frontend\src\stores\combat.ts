import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Monster, MonsterPart, CombatState } from '@/types'
import { combatApi } from '@/services/api'

export const useCombatStore = defineStore('combat', () => {
  // 狀態
  const monsters = ref<Monster[]>([])
  const monsterParts = ref<MonsterPart[]>([])
  const combatState = ref<CombatState>({
    isActive: false,
    monster: null,
    playerHp: 100,
    monsterHp: 0,
    combatLog: [],
    isPlayerTurn: true
  })
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 計算屬性
  const isActive = computed(() => combatState.value.isActive)
  const currentMonster = computed(() => combatState.value.monster)
  const playerHp = computed(() => combatState.value.playerHp)
  const monsterHp = computed(() => combatState.value.monsterHp)
  const combatLog = computed(() => combatState.value.combatLog)
  const isPlayerTurn = computed(() => combatState.value.isPlayerTurn)

  // 獲取怪物列表
  const fetchMonsters = async () => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await combatApi.getMonsters()
      monsters.value = response.data.monsters
      
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取怪物列表失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 獲取怪物部件
  const fetchMonsterParts = async () => {
    try {
      const response = await combatApi.getMonsterParts()
      monsterParts.value = response.data.parts
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取怪物部件失敗'
      throw err
    }
  }

  // 開始戰鬥
  const startCombat = async (monsterId: string) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await combatApi.startCombat(monsterId)
      const { combat } = response.data
      
      combatState.value = {
        isActive: true,
        monster: combat.monster,
        playerHp: combat.playerHp,
        monsterHp: combat.monsterHp,
        combatLog: [`一隻野生的 ${combat.monster.name} 出現了！`],
        isPlayerTurn: true
      }
      
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '開始戰鬥失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 玩家行動
  const playerAction = (action: 'attack' | 'flee', playerStats: any) => {
    if (!combatState.value.isActive || !combatState.value.isPlayerTurn || !combatState.value.monster) {
      return
    }

    if (action === 'flee') {
      endCombat('flee')
      return
    }

    // 攻擊邏輯
    const monster = combatState.value.monster
    const playerDamage = Math.max(1, playerStats.attack - monster.defense + (Math.floor(Math.random() * 5) - 2))
    const newMonsterHp = Math.max(0, combatState.value.monsterHp - playerDamage)
    
    combatState.value.monsterHp = newMonsterHp
    combatState.value.combatLog.push(`你攻擊並造成了 ${playerDamage} 點傷害。`)
    combatState.value.isPlayerTurn = false

    if (newMonsterHp <= 0) {
      setTimeout(() => endCombat('win'), 1000)
      return
    }

    // 怪物反擊
    setTimeout(() => {
      if (!combatState.value.isActive || !combatState.value.monster) return

      const monsterDamage = Math.max(1, monster.attack - playerStats.defense + (Math.floor(Math.random() * 3) - 1))
      const newPlayerHp = Math.max(0, combatState.value.playerHp - monsterDamage)
      
      combatState.value.playerHp = newPlayerHp
      combatState.value.combatLog.push(`${monster.name} 反擊，對你造成 ${monsterDamage} 點傷害。`)

      if (newPlayerHp <= 0) {
        setTimeout(() => endCombat('lose'), 1000)
      } else {
        combatState.value.isPlayerTurn = true
      }
    }, 1200)
  }

  // 結束戰鬥
  const endCombat = async (outcome: 'win' | 'lose' | 'flee') => {
    if (!combatState.value.monster) return

    try {
      const response = await combatApi.resolveCombat(combatState.value.monster.id, outcome)
      
      // 更新戰鬥日誌
      let message = ''
      switch (outcome) {
        case 'win':
          message = `你擊敗了 ${combatState.value.monster.name}!`
          break
        case 'lose':
          message = '你被擊敗了...'
          break
        case 'flee':
          message = '你成功逃跑了！'
          break
      }
      
      combatState.value.combatLog.push(message)
      combatState.value.isPlayerTurn = false

      // 延遲關閉戰鬥界面
      setTimeout(() => {
        combatState.value = {
          isActive: false,
          monster: null,
          playerHp: 100,
          monsterHp: 0,
          combatLog: [],
          isPlayerTurn: true
        }
      }, 2000)

      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '戰鬥結算失敗'
      throw err
    }
  }

  // 清除錯誤
  const clearError = () => {
    error.value = null
  }

  // 重置狀態
  const reset = () => {
    monsters.value = []
    monsterParts.value = []
    combatState.value = {
      isActive: false,
      monster: null,
      playerHp: 100,
      monsterHp: 0,
      combatLog: [],
      isPlayerTurn: true
    }
    error.value = null
    isLoading.value = false
  }

  return {
    // 狀態
    monsters,
    monsterParts,
    combatState,
    isLoading,
    error,
    
    // 計算屬性
    isActive,
    currentMonster,
    playerHp,
    monsterHp,
    combatLog,
    isPlayerTurn,
    
    // 方法
    fetchMonsters,
    fetchMonsterParts,
    startCombat,
    playerAction,
    endCombat,
    clearError,
    reset
  }
})

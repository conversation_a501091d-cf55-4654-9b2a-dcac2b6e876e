import express from 'express';
import { body, validationResult } from 'express-validator';
import Character from '../models/Character.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要認證
router.use(authenticateToken);

// 怪物數據（從原始constants移植）
const MONSTERS = [
  {
    id: 'giant_rat',
    name: '巨大老鼠',
    icon: '🐀',
    level: 1,
    hp: 30,
    attack: 6,
    defense: 2,
    huntCost: 5,
    xp: 10,
    drops: [
      { partId: 'rat_tail', chance: 0.8, quantity: [1, 2] },
    ]
  },
  {
    id: 'timber_wolf',
    name: '森林狼',
    icon: '🐺',
    level: 5,
    hp: 60,
    attack: 12,
    defense: 5,
    huntCost: 20,
    xp: 30,
    drops: [
      { partId: 'wolf_pelt', chance: 0.6, quantity: [1, 1] },
    ]
  },
  {
    id: 'risen_skeleton',
    name: '復活的骷髏',
    icon: '💀',
    level: 8,
    hp: 80,
    attack: 18,
    defense: 8,
    huntCost: 35,
    xp: 50,
    drops: [
      { partId: 'bone_fragment', chance: 0.9, quantity: [1, 3] },
    ]
  }
];

const MONSTER_PARTS = [
  { id: 'rat_tail', name: '老鼠尾巴', icon: '🐀' },
  { id: 'wolf_pelt', name: '狼皮', icon: '🐺' },
  { id: 'bone_fragment', name: '骨頭碎片', icon: '🦴' }
];

// 獲取所有怪物
router.get('/monsters', (req, res) => {
  try {
    res.json({
      monsters: MONSTERS
    });
  } catch (error) {
    console.error('獲取怪物列表錯誤:', error);
    res.status(500).json({
      message: '獲取怪物列表失敗'
    });
  }
});

// 獲取怪物部件
router.get('/parts', (req, res) => {
  try {
    res.json({
      parts: MONSTER_PARTS
    });
  } catch (error) {
    console.error('獲取怪物部件錯誤:', error);
    res.status(500).json({
      message: '獲取怪物部件失敗'
    });
  }
});

// 開始戰鬥
router.post('/start', [
  body('monsterId').notEmpty().withMessage('怪物ID是必需的')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: '輸入驗證失敗',
        errors: errors.array()
      });
    }

    const { monsterId } = req.body;
    
    const monster = MONSTERS.find(m => m.id === monsterId);
    if (!monster) {
      return res.status(404).json({
        message: '怪物不存在'
      });
    }

    const character = await Character.findOne({ userId: req.user._id });
    if (!character) {
      return res.status(404).json({
        message: '角色不存在'
      });
    }

    // 檢查等級要求
    if (character.level < monster.level) {
      return res.status(400).json({
        message: `需要等級 ${monster.level}`
      });
    }

    // 檢查水晶是否足夠
    if (character.crystals < monster.huntCost) {
      return res.status(400).json({
        message: '水晶不足'
      });
    }

    // 扣除狩獵成本
    character.crystals -= monster.huntCost;
    await character.save();

    // 返回戰鬥初始狀態
    res.json({
      message: '戰鬥開始',
      combat: {
        monster,
        playerHp: character.stats.maxHp,
        monsterHp: monster.hp,
        playerStats: {
          maxHp: character.stats.maxHp,
          attack: character.stats.baseAttack, // 這裡應該計算裝備加成
          defense: character.stats.baseDefense
        }
      },
      character
    });

  } catch (error) {
    console.error('開始戰鬥錯誤:', error);
    res.status(500).json({
      message: '開始戰鬥失敗'
    });
  }
});

// 戰鬥結算
router.post('/resolve', [
  body('monsterId').notEmpty().withMessage('怪物ID是必需的'),
  body('outcome').isIn(['win', 'lose', 'flee']).withMessage('戰鬥結果必須是win、lose或flee')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: '輸入驗證失敗',
        errors: errors.array()
      });
    }

    const { monsterId, outcome } = req.body;
    
    const monster = MONSTERS.find(m => m.id === monsterId);
    if (!monster) {
      return res.status(404).json({
        message: '怪物不存在'
      });
    }

    const character = await Character.findOne({ userId: req.user._id });
    if (!character) {
      return res.status(404).json({
        message: '角色不存在'
      });
    }

    const rewards = {
      xp: 0,
      loot: {},
      levelsGained: 0
    };

    if (outcome === 'win') {
      // 給予經驗值
      rewards.xp = monster.xp;
      rewards.levelsGained = character.addXp(monster.xp);

      // 計算戰利品
      monster.drops.forEach(drop => {
        if (Math.random() < drop.chance) {
          const quantity = Math.floor(Math.random() * (drop.quantity[1] - drop.quantity[0] + 1)) + drop.quantity[0];
          rewards.loot[drop.partId] = quantity;
          
          // 更新角色庫存
          const currentAmount = character.inventory.get(drop.partId) || 0;
          character.inventory.set(drop.partId, currentAmount + quantity);
        }
      });

      await character.save();
    }

    res.json({
      message: `戰鬥${outcome === 'win' ? '勝利' : outcome === 'lose' ? '失敗' : '逃跑'}`,
      outcome,
      rewards,
      character
    });

  } catch (error) {
    console.error('戰鬥結算錯誤:', error);
    res.status(500).json({
      message: '戰鬥結算失敗'
    });
  }
});

export default router;

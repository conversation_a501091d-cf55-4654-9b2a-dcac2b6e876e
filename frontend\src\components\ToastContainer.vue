<template>
  <div class="fixed top-4 right-4 z-50 space-y-2">
    <TransitionGroup
      name="toast"
      tag="div"
      class="space-y-2"
    >
      <div
        v-for="toast in toastStore.toasts"
        :key="toast.id"
        :class="[
          'toast',
          `toast-${toast.type}`,
          'flex items-center justify-between p-4 rounded-lg shadow-lg max-w-sm min-w-[300px]'
        ]"
      >
        <div class="flex items-center space-x-3">
          <!-- 圖標 -->
          <div class="flex-shrink-0">
            <component :is="getIcon(toast.type)" class="w-5 h-5" />
          </div>
          
          <!-- 消息內容 -->
          <div class="flex-1">
            <p class="text-sm font-medium">{{ toast.message }}</p>
          </div>
        </div>

        <!-- 關閉按鈕 -->
        <button
          @click="toastStore.removeToast(toast.id)"
          class="flex-shrink-0 ml-3 p-1 rounded-full hover:bg-black/20 transition-colors"
        >
          <XMarkIcon class="w-4 h-4" />
        </button>
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { useToastStore } from '@/stores/toast'
import type { ToastType } from '@/types'
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  SparklesIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

const toastStore = useToastStore()

const getIcon = (type: ToastType) => {
  switch (type) {
    case 'success':
      return CheckCircleIcon
    case 'error':
      return ExclamationCircleIcon
    case 'warning':
      return ExclamationTriangleIcon
    case 'info':
      return InformationCircleIcon
    case 'reward':
      return SparklesIcon
    default:
      return InformationCircleIcon
  }
}
</script>

<style scoped>
.toast-success {
  @apply bg-green-600 text-white;
}

.toast-error {
  @apply bg-red-600 text-white;
}

.toast-warning {
  @apply bg-yellow-600 text-white;
}

.toast-info {
  @apply bg-blue-600 text-white;
}

.toast-reward {
  @apply bg-purple-600 text-white;
}

/* 動畫 */
.toast-enter-active {
  transition: all 0.3s ease-out;
}

.toast-leave-active {
  transition: all 0.3s ease-in;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.toast-move {
  transition: transform 0.3s ease;
}
</style>

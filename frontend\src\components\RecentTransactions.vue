<template>
  <div class="card">
    <h2 class="text-xl font-bold text-yellow-400 font-pixel mb-4">最近交易</h2>
    
    <div v-if="transactionStore.isLoading" class="flex justify-center py-8">
      <div class="loading-spinner"></div>
    </div>
    
    <div v-else-if="transactionStore.transactions.length > 0" class="space-y-3">
      <div 
        v-for="transaction in recentTransactions" 
        :key="transaction.id"
        class="flex items-center justify-between p-3 bg-stone-700/50 rounded-lg"
      >
        <div class="flex items-center space-x-3">
          <div class="text-2xl">
            {{ getCategoryIcon(transaction.category) }}
          </div>
          <div>
            <div class="font-medium text-stone-200">{{ transaction.description }}</div>
            <div class="text-xs text-stone-400">
              {{ getCategoryLabel(transaction.category) }} • {{ formatDate(transaction.date) }}
            </div>
          </div>
        </div>
        
        <div class="text-right">
          <div 
            :class="[
              'font-bold',
              transaction.type === TransactionType.INCOME ? 'text-green-400' : 'text-red-400'
            ]"
          >
            {{ transaction.type === TransactionType.INCOME ? '+' : '-' }}{{ formatCurrency(transaction.amount) }}
          </div>
        </div>
      </div>
      
      <router-link 
        to="/transactions" 
        class="block text-center text-yellow-400 hover:text-yellow-300 text-sm font-medium py-2 transition-colors"
      >
        查看所有交易 →
      </router-link>
    </div>
    
    <div v-else class="text-center py-8 text-stone-400">
      還沒有任何交易記錄
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useTransactionStore } from '@/stores/transaction'
import { TransactionType, Category } from '@/types'

const transactionStore = useTransactionStore()

const recentTransactions = computed(() => {
  return transactionStore.transactions.slice(0, 5)
})

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-TW', {
    style: 'currency',
    currency: 'TWD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW', {
    month: 'short',
    day: 'numeric'
  })
}

const getCategoryIcon = (category: Category) => {
  const icons: Record<Category, string> = {
    [Category.SALARY]: '💼',
    [Category.INVESTMENT]: '📈',
    [Category.BUSINESS]: '🏪',
    [Category.OTHER_INCOME]: '💰',
    [Category.FOOD]: '🍽️',
    [Category.TRANSPORT]: '🚗',
    [Category.ENTERTAINMENT]: '🎮',
    [Category.SHOPPING]: '🛒',
    [Category.BILLS]: '📄',
    [Category.HEALTHCARE]: '🏥',
    [Category.EDUCATION]: '📚',
    [Category.OTHER_EXPENSE]: '💸'
  }
  return icons[category] || '❓'
}

const getCategoryLabel = (category: Category) => {
  const labels: Record<Category, string> = {
    [Category.SALARY]: '薪資',
    [Category.INVESTMENT]: '投資',
    [Category.BUSINESS]: '營業',
    [Category.OTHER_INCOME]: '其他收入',
    [Category.FOOD]: '餐飲',
    [Category.TRANSPORT]: '交通',
    [Category.ENTERTAINMENT]: '娛樂',
    [Category.SHOPPING]: '購物',
    [Category.BILLS]: '帳單',
    [Category.HEALTHCARE]: '醫療',
    [Category.EDUCATION]: '教育',
    [Category.OTHER_EXPENSE]: '其他支出'
  }
  return labels[category] || '未知'
}
</script>

<style scoped>
.loading-spinner {
  @apply border-4 border-stone-600 border-t-yellow-400 rounded-full w-6 h-6 animate-spin;
}
</style>

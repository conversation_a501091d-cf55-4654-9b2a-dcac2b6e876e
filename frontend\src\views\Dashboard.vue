<template>
  <div class="p-4 md:p-8">
    <!-- 歡迎標題 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-yellow-400 font-pixel mb-2">
        歡迎回來，{{ authStore.user?.username }}！
      </h1>
      <p class="text-stone-400">
        繼續你的財富冒險之旅
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- 左側主要內容 -->
      <div class="lg:col-span-2 space-y-8">
        <!-- 角色狀態卡片 -->
        <CharacterStatusCard />
        
        <!-- 財務概覽 -->
        <FinancialOverview />
        
        <!-- 任務板 -->
        <QuestBoard />
      </div>

      <!-- 右側側邊欄 -->
      <div class="space-y-8">
        <!-- 交易表單 -->
        <TransactionForm @transaction-added="handleTransactionAdded" />
        
        <!-- 最近交易 -->
        <RecentTransactions />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useCharacterStore } from '@/stores/character'
import { useTransactionStore } from '@/stores/transaction'
import { useToastStore } from '@/stores/toast'
import CharacterStatusCard from '@/components/CharacterStatusCard.vue'
import FinancialOverview from '@/components/FinancialOverview.vue'
import QuestBoard from '@/components/QuestBoard.vue'
import TransactionForm from '@/components/TransactionForm.vue'
import RecentTransactions from '@/components/RecentTransactions.vue'

const authStore = useAuthStore()
const characterStore = useCharacterStore()
const transactionStore = useTransactionStore()
const toastStore = useToastStore()

const handleTransactionAdded = () => {
  // 重新獲取交易數據和角色信息
  transactionStore.fetchTransactions()
  characterStore.fetchCharacter()
}

onMounted(async () => {
  try {
    // 並行獲取數據
    await Promise.all([
      characterStore.fetchCharacter(),
      transactionStore.fetchTransactions({ limit: 10 })
    ])
  } catch (error) {
    console.error('載入儀表板數據失敗:', error)
    toastStore.error('載入數據失敗，請重新整理頁面')
  }
})
</script>

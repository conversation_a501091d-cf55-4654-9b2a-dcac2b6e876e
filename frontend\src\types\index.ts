// 用戶相關類型
export interface User {
  id: string
  username: string
  email: string
  avatar: string
  lastLogin: string
  preferences: {
    theme: 'dark' | 'light'
    language: 'zh-TW' | 'zh-CN' | 'en'
    notifications: boolean
  }
}

// 交易相關類型
export enum TransactionType {
  INCOME = 'income',
  EXPENSE = 'expense',
}

export enum Category {
  // 收入類別
  SALARY = 'salary',
  INVESTMENT = 'investment',
  BUSINESS = 'business',
  OTHER_INCOME = 'other_income',

  // 支出類別
  FOOD = 'food',
  TRANSPORT = 'transport',
  ENTERTAINMENT = 'entertainment',
  SHOPPING = 'shopping',
  BILLS = 'bills',
  HEALTHCARE = 'healthcare',
  EDUCATION = 'education',
  OTHER_EXPENSE = 'other_expense'
}

export interface Transaction {
  id: string
  userId: string
  description: string
  amount: number
  type: TransactionType
  category: Category
  tags?: string[]
  location?: string
  notes?: string
  xpAwarded?: number
  crystalsAwarded?: number
  createdAt: string
  updatedAt: string
}

// 角色相關類型
export interface Character {
  id: string
  userId: string
  level: number
  xp: number
  crystals: number
  talentPoints: number
  stats: {
    maxHp: number
    baseAttack: number
    baseDefense: number
  }
  equipment: {
    weapon: string | null
    armor: string | null
  }
  ownedEquipment: string[]
  unlockedTalents: string[]
  inventory: Record<string, number>
  builtBuildings: string[]
  uncollectedTaxes: number
  completedQuests: Array<{
    questId: string
    completedAt: string
  }>
  achievements: Array<{
    id: string
    unlockedAt: string
  }>
  lastTaxCollection: string
  xpForNextLevel: number
  totalAttack: number
  totalDefense: number
  createdAt: string
  updatedAt: string
}

// 裝備相關類型
export enum EquipmentSlot {
  WEAPON = 'weapon',
  ARMOR = 'armor',
}

export interface EquipmentItem {
  id: string
  name: string
  icon: string
  cost: number
  slot: EquipmentSlot
  attack?: number
  defense?: number
  bonus?: {
    type: string
    value: number
    description: string
  }
}

// 任務相關類型
export interface Quest {
  id: string
  title: string
  description: string
  reward: number
  condition: {
    type: 'earn' | 'spend' | 'balance'
    amount: number
    category?: Category
  }
}

// 建築相關類型
export interface Building {
  id: string
  name: string
  description: string
  cost: number
  requiredLevel: number
  generationRate?: number
}

// 天賦相關類型
export enum TalentEffectType {
  SHOP_COST_REDUCTION = 'shop_cost_reduction',
  CRYSTAL_CHANCE_ON_TRANSACTION = 'crystal_chance_on_transaction',
  XP_GAIN_INCREASE = 'xp_gain_increase',
  QUEST_XP_BONUS = 'quest_xp_bonus',
  BUILDING_COST_REDUCTION = 'building_cost_reduction',
  TAX_RATE_INCREASE = 'tax_rate_increase',
}

export interface Talent {
  id: string
  name: string
  description: string
  icon: string
  cost: number
  requiredLevel: number
  prerequisite?: string
  effect: {
    type: TalentEffectType
    value: number
  }
  position: {
    row: number
    col: number
  }
}

// 怪物相關類型
export interface MonsterPart {
  id: string
  name: string
  icon: string
}

export interface Monster {
  id: string
  name: string
  icon: string
  level: number
  hp: number
  attack: number
  defense: number
  huntCost: number
  xp: number
  drops: Array<{
    partId: string
    chance: number
    quantity: [number, number]
  }>
}

export interface Location {
  id: string
  name: string
  requiredLevel: number
  monsters: string[]
}

// 戰鬥相關類型
export interface CombatState {
  isActive: boolean
  monster: Monster | null
  playerHp: number
  monsterHp: number
  combatLog: string[]
  isPlayerTurn: boolean
}

// 通知相關類型
export type ToastType = 'success' | 'error' | 'warning' | 'info' | 'reward'

export interface ToastMessage {
  id: string
  message: string
  type: ToastType
  duration?: number
}

// API 響應類型
export interface ApiResponse<T = any> {
  message: string
  data?: T
  errors?: Array<{
    field: string
    message: string
  }>
}

export interface PaginationInfo {
  current: number
  pages: number
  total: number
  hasNext: boolean
  hasPrev: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: PaginationInfo
}

// 財務統計類型
export interface FinancialStats {
  totalIncome: number
  totalExpenses: number
  balance: number
  incomeByCategory: Record<Category, number>
  expenseByCategory: Record<Category, number>
}

// 獎勵類型
export interface Rewards {
  xp?: number
  crystals?: number
  levelsGained?: number
  newLevel?: number
  loot?: Record<string, number>
}

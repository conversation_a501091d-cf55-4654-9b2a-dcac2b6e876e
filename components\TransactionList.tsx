import React from 'react';
import { Transaction, TransactionType } from '../types';
import CategoryIcon from './CategoryIcon';

interface TransactionListProps {
    transactions: Transaction[];
    onDeleteTransaction: (id: string) => void;
}

const TransactionList: React.FC<TransactionListProps> = ({ transactions, onDeleteTransaction }) => {
    return (
        <div>
            <h2 className="text-2xl font-bold mb-4 text-yellow-400 font-pixel">交易日誌</h2>
            <div className="max-h-[400px] overflow-y-auto pr-2 space-y-3">
                {transactions.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">尚未記錄任何交易。</p>
                ) : (
                    transactions.map(t => (
                        <div key={t.id} className="bg-stone-900/50 p-3 rounded-lg flex items-center justify-between transition-colors hover:bg-stone-700/50">
                            <div className="flex items-center space-x-4">
                                <span className="text-2xl"><CategoryIcon category={t.category} /></span>
                                <div>
                                    <p className="font-semibold">{t.description}</p>
                                    <p className="text-sm text-gray-400">{new Date(t.date).toLocaleDateString()}</p>
                                </div>
                            </div>
                            <div className="text-right">
                                <p className={`font-bold font-pixel ${t.type === TransactionType.INCOME ? 'text-green-400' : 'text-red-400'}`}>
                                    {t.type === TransactionType.INCOME ? '+' : '-'} {t.amount} G
                                </p>
                                <button
                                    onClick={() => onDeleteTransaction(t.id)}
                                    className="text-xs text-gray-500 hover:text-red-400 transition-colors"
                                >
                                    刪除
                                </button>
                            </div>
                        </div>
                    ))
                )}
            </div>
        </div>
    );
};

export default TransactionList;

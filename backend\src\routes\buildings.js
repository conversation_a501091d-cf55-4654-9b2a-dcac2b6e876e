import express from 'express';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 建築數據（從原始constants移植）
const BUILDINGS = [
  {
    id: 'market',
    name: '市集',
    description: '建造一個市集以開始徵收稅金。',
    cost: 200,
    requiredLevel: 5,
    generationRate: 0.5, // 0.5 crystal per second
  }
];

// 獲取所有建築
router.get('/', (req, res) => {
  try {
    res.json({
      buildings: BUILDINGS
    });
  } catch (error) {
    console.error('獲取建築列表錯誤:', error);
    res.status(500).json({
      message: '獲取建築列表失敗'
    });
  }
});

// 獲取單個建築詳情
router.get('/:id', (req, res) => {
  try {
    const building = BUILDINGS.find(b => b.id === req.params.id);
    
    if (!building) {
      return res.status(404).json({
        message: '建築不存在'
      });
    }

    res.json(building);
  } catch (error) {
    console.error('獲取建築詳情錯誤:', error);
    res.status(500).json({
      message: '獲取建築詳情失敗'
    });
  }
});

export default router;

import express from 'express';
import { body, query, validationResult } from 'express-validator';
import Transaction from '../models/Transaction.js';
import Character from '../models/Character.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要認證
router.use(authenticateToken);

// 獲取交易列表
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('頁碼必須是正整數'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每頁數量必須在1-100之間'),
  query('type').optional().isIn(['income', 'expense']).withMessage('類型必須是income或expense'),
  query('category').optional().isString(),
  query('startDate').optional().isISO8601().withMessage('開始日期格式錯誤'),
  query('endDate').optional().isISO8601().withMessage('結束日期格式錯誤')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: '查詢參數錯誤',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      type,
      category,
      startDate,
      endDate,
      search
    } = req.query;

    // 構建查詢條件
    const query = { userId: req.user._id };
    
    if (type) query.type = type;
    if (category) query.category = category;
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    if (search) {
      query.$or = [
        { description: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } }
      ];
    }

    // 執行查詢
    const skip = (page - 1) * limit;
    const [transactions, total] = await Promise.all([
      Transaction.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      Transaction.countDocuments(query)
    ]);

    res.json({
      transactions,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total,
        hasNext: skip + transactions.length < total,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('獲取交易列表錯誤:', error);
    res.status(500).json({
      message: '獲取交易列表失敗'
    });
  }
});

// 創建新交易
router.post('/', [
  body('description')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('描述必須在1-200個字符之間'),
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('金額必須大於0'),
  body('type')
    .isIn(['income', 'expense'])
    .withMessage('類型必須是income或expense'),
  body('category')
    .isIn([
      '薪水', '任務獎勵', '稅金',
      '補給品', '裝備', '建設', '餐飲', '交通', '娛樂', '狩獵', '其他'
    ])
    .withMessage('無效的交易類別'),
  body('notes').optional().isLength({ max: 500 }).withMessage('備註不能超過500個字符')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: '輸入驗證失敗',
        errors: errors.array()
      });
    }

    const { description, amount, type, category, notes, tags } = req.body;

    // 創建交易
    const transaction = new Transaction({
      userId: req.user._id,
      description,
      amount,
      type,
      category,
      notes,
      tags: tags || []
    });

    await transaction.save();

    // 獲取角色並給予經驗值
    const character = await Character.findOne({ userId: req.user._id });
    if (character) {
      const xpGain = 10; // 基礎經驗值
      const levelsGained = character.addXp(xpGain);
      
      // 更新交易記錄的經驗值
      transaction.xpAwarded = xpGain;
      
      // 檢查天賦加成（水晶機率）
      // 這裡需要實現天賦系統的邏輯
      
      await Promise.all([
        transaction.save(),
        character.save()
      ]);

      res.status(201).json({
        message: '交易創建成功',
        transaction,
        rewards: {
          xp: xpGain,
          levelsGained,
          newLevel: character.level
        }
      });
    } else {
      res.status(201).json({
        message: '交易創建成功',
        transaction
      });
    }

  } catch (error) {
    console.error('創建交易錯誤:', error);
    res.status(500).json({
      message: '創建交易失敗'
    });
  }
});

// 獲取單個交易
router.get('/:id', async (req, res) => {
  try {
    const transaction = await Transaction.findOne({
      _id: req.params.id,
      userId: req.user._id
    });

    if (!transaction) {
      return res.status(404).json({
        message: '交易不存在'
      });
    }

    res.json(transaction);

  } catch (error) {
    console.error('獲取交易錯誤:', error);
    res.status(500).json({
      message: '獲取交易失敗'
    });
  }
});

// 更新交易
router.put('/:id', [
  body('description')
    .optional()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('描述必須在1-200個字符之間'),
  body('amount')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('金額必須大於0'),
  body('type')
    .optional()
    .isIn(['income', 'expense'])
    .withMessage('類型必須是income或expense'),
  body('category')
    .optional()
    .isIn([
      '薪水', '任務獎勵', '稅金',
      '補給品', '裝備', '建設', '餐飲', '交通', '娛樂', '狩獵', '其他'
    ])
    .withMessage('無效的交易類別')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: '輸入驗證失敗',
        errors: errors.array()
      });
    }

    const transaction = await Transaction.findOneAndUpdate(
      { _id: req.params.id, userId: req.user._id },
      req.body,
      { new: true, runValidators: true }
    );

    if (!transaction) {
      return res.status(404).json({
        message: '交易不存在'
      });
    }

    res.json({
      message: '交易更新成功',
      transaction
    });

  } catch (error) {
    console.error('更新交易錯誤:', error);
    res.status(500).json({
      message: '更新交易失敗'
    });
  }
});

// 刪除交易
router.delete('/:id', async (req, res) => {
  try {
    const transaction = await Transaction.findOneAndDelete({
      _id: req.params.id,
      userId: req.user._id
    });

    if (!transaction) {
      return res.status(404).json({
        message: '交易不存在'
      });
    }

    res.json({
      message: '交易刪除成功'
    });

  } catch (error) {
    console.error('刪除交易錯誤:', error);
    res.status(500).json({
      message: '刪除交易失敗'
    });
  }
});

// 獲取財務統計
router.get('/stats/summary', async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const stats = await Transaction.getFinancialStats(
      req.user._id,
      startDate ? new Date(startDate) : null,
      endDate ? new Date(endDate) : null
    );

    const categoryStats = await Promise.all([
      Transaction.getCategoryStats(req.user._id, 'income', 
        startDate ? new Date(startDate) : null,
        endDate ? new Date(endDate) : null
      ),
      Transaction.getCategoryStats(req.user._id, 'expense',
        startDate ? new Date(startDate) : null,
        endDate ? new Date(endDate) : null
      )
    ]);

    res.json({
      summary: stats,
      incomeByCategory: categoryStats[0],
      expenseByCategory: categoryStats[1]
    });

  } catch (error) {
    console.error('獲取統計錯誤:', error);
    res.status(500).json({
      message: '獲取統計失敗'
    });
  }
});

export default router;

<template>
  <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- 標題 -->
      <div class="text-center">
        <h1 class="text-4xl font-bold text-yellow-500 font-pixel mb-2">
          財富冒險
        </h1>
        <h2 class="text-2xl font-bold text-stone-100 mb-2">
          開始你的冒險！
        </h2>
        <p class="text-stone-400">
          創建帳戶，踏上財富管理的奇幻之旅
        </p>
      </div>

      <!-- 註冊表單 -->
      <form @submit.prevent="handleRegister" class="mt-8 space-y-6">
        <div class="card">
          <div class="space-y-4">
            <!-- 用戶名 -->
            <div>
              <label for="username" class="block text-sm font-medium text-stone-300 mb-2">
                用戶名
              </label>
              <input
                id="username"
                v-model="form.username"
                type="text"
                required
                class="input-field"
                placeholder="請輸入用戶名（3-30個字符）"
                :disabled="authStore.isLoading"
                minlength="3"
                maxlength="30"
              />
            </div>

            <!-- 電子郵件 -->
            <div>
              <label for="email" class="block text-sm font-medium text-stone-300 mb-2">
                電子郵件
              </label>
              <input
                id="email"
                v-model="form.email"
                type="email"
                required
                class="input-field"
                placeholder="請輸入你的電子郵件"
                :disabled="authStore.isLoading"
              />
            </div>

            <!-- 密碼 -->
            <div>
              <label for="password" class="block text-sm font-medium text-stone-300 mb-2">
                密碼
              </label>
              <input
                id="password"
                v-model="form.password"
                type="password"
                required
                class="input-field"
                placeholder="請輸入密碼（至少6個字符）"
                :disabled="authStore.isLoading"
                minlength="6"
              />
            </div>

            <!-- 確認密碼 -->
            <div>
              <label for="confirmPassword" class="block text-sm font-medium text-stone-300 mb-2">
                確認密碼
              </label>
              <input
                id="confirmPassword"
                v-model="form.confirmPassword"
                type="password"
                required
                class="input-field"
                placeholder="請再次輸入密碼"
                :disabled="authStore.isLoading"
              />
              <div v-if="form.confirmPassword && form.password !== form.confirmPassword" 
                   class="text-red-400 text-sm mt-1">
                密碼不一致
              </div>
            </div>

            <!-- 錯誤訊息 -->
            <div v-if="authStore.error" class="text-red-400 text-sm text-center">
              {{ authStore.error }}
            </div>

            <!-- 註冊按鈕 -->
            <button
              type="submit"
              :disabled="authStore.isLoading || !isFormValid"
              class="w-full btn-primary flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div v-if="authStore.isLoading" class="loading-spinner"></div>
              <span>{{ authStore.isLoading ? '註冊中...' : '註冊' }}</span>
            </button>
          </div>
        </div>

        <!-- 登入連結 -->
        <div class="text-center">
          <p class="text-stone-400">
            已經有帳戶？
            <router-link 
              to="/login" 
              class="text-yellow-400 hover:text-yellow-300 font-medium transition-colors"
            >
              立即登入
            </router-link>
          </p>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useToastStore } from '@/stores/toast'

const router = useRouter()
const authStore = useAuthStore()
const toastStore = useToastStore()

const form = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const isFormValid = computed(() => {
  return form.username.length >= 3 &&
         form.email.includes('@') &&
         form.password.length >= 6 &&
         form.password === form.confirmPassword
})

const handleRegister = async () => {
  if (!isFormValid.value) {
    toastStore.error('請檢查表單內容')
    return
  }

  try {
    authStore.clearError()
    await authStore.register({
      username: form.username,
      email: form.email,
      password: form.password
    })
    
    toastStore.success('註冊成功！歡迎加入冒險者行列！')
    router.push('/dashboard')
  } catch (error: any) {
    toastStore.error(authStore.error || '註冊失敗，請稍後再試')
  }
}

onMounted(() => {
  // 清除之前的錯誤
  authStore.clearError()
})
</script>

<style scoped>
.loading-spinner {
  @apply border-2 border-white border-t-transparent rounded-full w-4 h-4 animate-spin;
}
</style>

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Character, EquipmentItem, Talent, Building, Rewards } from '@/types'
import { characterApi } from '@/services/api'

export const useCharacterStore = defineStore('character', () => {
  // 狀態
  const character = ref<Character | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 計算屬性
  const level = computed(() => character.value?.level || 1)
  const xp = computed(() => character.value?.xp || 0)
  const crystals = computed(() => character.value?.crystals || 0)
  const talentPoints = computed(() => character.value?.talentPoints || 0)
  const xpForNextLevel = computed(() => character.value?.xpForNextLevel || 100)
  const totalAttack = computed(() => character.value?.totalAttack || 5)
  const totalDefense = computed(() => character.value?.totalDefense || 2)
  const balance = computed(() => {
    // 這裡需要從交易數據計算，暫時返回0
    return 0
  })

  // 天賦加成計算
  const talentBonuses = computed(() => {
    if (!character.value) {
      return {
        shopCostReduction: 0,
        crystalChance: 0,
        xpGainIncrease: 0,
        questXpBonus: 0,
        buildingCostReduction: 0,
        taxRateIncrease: 0
      }
    }

    // 這裡需要根據解鎖的天賦計算加成
    // 暫時返回默認值
    return {
      shopCostReduction: 0,
      crystalChance: 0,
      xpGainIncrease: 0,
      questXpBonus: 0,
      buildingCostReduction: 0,
      taxRateIncrease: 0
    }
  })

  // 獲取角色信息
  const fetchCharacter = async () => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await characterApi.getCharacter()
      character.value = response.data
      
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取角色信息失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 更新角色信息
  const updateCharacter = async (updates: Partial<Character>) => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await characterApi.updateCharacter(updates)
      character.value = response.data.character
      
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || '更新角色信息失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 裝備物品
  const equipItem = async (itemId: string, slot: 'weapon' | 'armor') => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await characterApi.equipItem(itemId, slot)
      character.value = response.data.character
      
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || '裝備失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 卸下裝備
  const unequipItem = async (slot: 'weapon' | 'armor') => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await characterApi.unequipItem(slot)
      character.value = response.data.character
      
      return response
    } catch (err: any) {
      error.value = err.response?.data?.message || '卸下裝備失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 購買裝備
  const buyEquipment = async (item: EquipmentItem): Promise<{ character: Character; rewards: Rewards }> => {
    try {
      isLoading.value = true
      error.value = null
      
      const finalCost = Math.ceil(item.cost * (1 - talentBonuses.value.shopCostReduction))
      const response = await characterApi.buyEquipment(item.id, finalCost)
      character.value = response.data.character
      
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '購買失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 解鎖天賦
  const unlockTalent = async (talent: Talent): Promise<{ character: Character }> => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await characterApi.unlockTalent(
        talent.id,
        talent.cost,
        talent.requiredLevel,
        talent.prerequisite
      )
      character.value = response.data.character
      
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '解鎖天賦失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 建造建築
  const buildBuilding = async (building: Building): Promise<{ character: Character; rewards: Rewards }> => {
    try {
      isLoading.value = true
      error.value = null
      
      const finalCost = Math.ceil(building.cost * (1 - talentBonuses.value.buildingCostReduction))
      const response = await characterApi.buildBuilding(
        building.id,
        finalCost,
        building.requiredLevel
      )
      character.value = response.data.character
      
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '建造失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 收集稅收
  const collectTaxes = async (): Promise<{ character: Character; collectedAmount: number }> => {
    try {
      isLoading.value = true
      error.value = null
      
      const response = await characterApi.collectTaxes()
      character.value = response.data.character
      
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '收集稅收失敗'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // 清除錯誤
  const clearError = () => {
    error.value = null
  }

  // 重置狀態
  const reset = () => {
    character.value = null
    error.value = null
    isLoading.value = false
  }

  return {
    // 狀態
    character,
    isLoading,
    error,
    
    // 計算屬性
    level,
    xp,
    crystals,
    talentPoints,
    xpForNextLevel,
    totalAttack,
    totalDefense,
    balance,
    talentBonuses,
    
    // 方法
    fetchCharacter,
    updateCharacter,
    equipItem,
    unequipItem,
    buyEquipment,
    unlockTalent,
    buildBuilding,
    collectTaxes,
    clearError,
    reset
  }
})
